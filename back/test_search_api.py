#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试项目报价审批API搜索功能
"""

import requests
import json
from urllib.parse import quote





def test_search_functionality():
    """
    测试搜索功能
    """
    base_url = "http://localhost:9099"
    endpoint = "/quotation/project-quotation-approval/pending"
    
    # 直接使用测试token
    token = "test_token"
    print(f"使用测试token: {token}")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试1: 不带搜索条件，获取所有数据
    print("\n=== 测试1: 获取所有待审批项目 ===")
    params = {
        "pageNum": 1,
        "pageSize": 20
    }
    
    all_projects = []
    try:
        response = requests.get(f"{base_url}{endpoint}", params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    result_data = data['data']
                    if isinstance(result_data, dict) and 'list' in result_data:
                        all_projects = result_data['list']
                        print(f"总共找到 {len(all_projects)} 个待审批项目:")
                        for i, project in enumerate(all_projects):
                            project_name = project.get('projectName', project.get('project_name', 'N/A'))
                            print(f"  {i+1}. {project_name}")
            except Exception as parse_error:
                print(f"解析响应失败: {parse_error}")
        
    except Exception as e:
        print(f"请求失败: {e}")
    
    # 测试2: 使用具体的项目名称进行搜索
    if all_projects:
        # 取第一个项目的名称进行搜索测试
        test_project = all_projects[0]
        test_project_name = test_project.get('projectName', test_project.get('project_name', ''))
        
        if test_project_name:
            print(f"\n=== 测试2: 搜索项目名称包含 '{test_project_name}' 的项目 ===")
            params = {
                "pageNum": 1,
                "pageSize": 10,
                "projectName": test_project_name
            }
            
            try:
                response = requests.get(f"{base_url}{endpoint}", params=params, headers=headers)
                print(f"状态码: {response.status_code}")
                print(f"查询参数: {params}")
                print(f"响应内容: {response.text}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        if isinstance(data, dict) and 'data' in data:
                            result_data = data['data']
                            if isinstance(result_data, dict) and 'list' in result_data:
                                filtered_projects = result_data['list']
                                print(f"\n搜索结果: 找到 {len(filtered_projects)} 个匹配项目:")
                                
                                if len(filtered_projects) == len(all_projects):
                                    print("⚠️  警告: 搜索返回了所有项目，搜索功能可能没有生效!")
                                    print("返回的项目:")
                                    for project in filtered_projects:
                                        project_name = project.get('projectName', project.get('project_name', 'N/A'))
                                        print(f"  - {project_name}")
                                else:
                                    print("✓ 搜索功能正常，返回了过滤后的结果:")
                                    for project in filtered_projects:
                                        project_name = project.get('projectName', project.get('project_name', 'N/A'))
                                        print(f"  - {project_name}")
                                        if test_project_name.lower() in project_name.lower():
                                            print(f"    ✓ 匹配")
                                        else:
                                            print(f"    ✗ 不匹配")
                    except Exception as parse_error:
                        print(f"解析响应失败: {parse_error}")
                
            except Exception as e:
                print(f"请求失败: {e}")
    
    # 测试3: 使用部分项目名称进行搜索
    print(f"\n=== 测试3: 搜索项目名称包含 '测试' 的项目 ===")
    params = {
        "pageNum": 1,
        "pageSize": 10,
        "projectName": "测试"
    }
    
    try:
        response = requests.get(f"{base_url}{endpoint}", params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"查询参数: {params}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    result_data = data['data']
                    if isinstance(result_data, dict) and 'list' in result_data:
                        filtered_projects = result_data['list']
                        print(f"\n搜索结果: 找到 {len(filtered_projects)} 个包含'测试'的项目:")
                        
                        for project in filtered_projects:
                            project_name = project.get('projectName', project.get('project_name', 'N/A'))
                            print(f"  - {project_name}")
                            if "测试" in project_name:
                                print(f"    ✓ 包含'测试'")
                            else:
                                print(f"    ✗ 不包含'测试'")
            except Exception as parse_error:
                print(f"解析响应失败: {parse_error}")
        
    except Exception as e:
        print(f"请求失败: {e}")


if __name__ == "__main__":
    test_search_functionality()