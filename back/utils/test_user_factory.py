#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户工厂类

用于创建标准化的测试用户，支持测试环境下的认证跳过功能。
将测试用户创建逻辑从业务代码中分离，提高代码的可维护性和可测试性。
"""

from typing import Optional, List, Dict, Any
from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
from module_admin.entity.vo.dept_vo import DeptModel
from module_admin.entity.vo.role_vo import RoleModel
from utils.log_util import logger


class TestUserFactory:
    """测试用户工厂类"""
    
    # 默认测试用户配置
    DEFAULT_TEST_USER_CONFIG = {
        'user_id': 999,
        'user_name': 'test_user',
        'nick_name': '测试用户',
        'email': '<EMAIL>',
        'phonenumber': '13800138000',
        'sex': '0',
        'avatar': '',
        'dept_id': 1,
        'status': '0',
        'admin': True
    }
    
    DEFAULT_TEST_DEPT_CONFIG = {
        'dept_id': 1,
        'dept_name': '测试部门',
        'parent_id': 0,
        'order_num': 1,
        'leader': 999,
        'phone': '13800138000',
        'email': '<EMAIL>',
        'status': '0'
    }
    
    DEFAULT_TEST_ROLE_CONFIG = {
        'role_id': 999,
        'role_name': '测试角色',
        'role_key': 'test_role',
        'role_sort': 1,
        'data_scope': '1',
        'status': '0'
    }
    
    @classmethod
    def create_test_user(cls, 
                        user_config: Optional[Dict[str, Any]] = None,
                        dept_config: Optional[Dict[str, Any]] = None,
                        role_config: Optional[Dict[str, Any]] = None,
                        permissions: Optional[List[str]] = None,
                        roles: Optional[List[str]] = None) -> CurrentUserModel:
        """
        创建标准测试用户
        
        Args:
            user_config: 用户配置覆盖
            dept_config: 部门配置覆盖
            role_config: 角色配置覆盖
            permissions: 权限列表
            roles: 角色列表
            
        Returns:
            CurrentUserModel: 完整的测试用户模型
        """
        # 合并配置
        final_user_config = {**cls.DEFAULT_TEST_USER_CONFIG}
        if user_config:
            final_user_config.update(user_config)
            
        final_dept_config = {**cls.DEFAULT_TEST_DEPT_CONFIG}
        if dept_config:
            final_dept_config.update(dept_config)
            
        final_role_config = {**cls.DEFAULT_TEST_ROLE_CONFIG}
        if role_config:
            final_role_config.update(role_config)
        
        # 创建部门模型
        test_dept = DeptModel(**final_dept_config)
        
        # 创建角色模型
        test_role = RoleModel(**final_role_config)
        
        # 创建用户信息模型
        test_user_info = UserInfoModel(
            **final_user_config,
            dept=test_dept,
            role=[test_role]
        )
        
        # 创建当前用户模型
        test_user = CurrentUserModel(
            permissions=permissions or ["*:*:*"],
            roles=roles or ["test_role"],
            user=test_user_info
        )
        
        logger.info(f"创建测试用户: {test_user_info.user_name} (ID: {test_user_info.user_id})")
        return test_user
    
    @classmethod
    def create_admin_test_user(cls) -> CurrentUserModel:
        """
        创建管理员测试用户
        
        Returns:
            CurrentUserModel: 具有管理员权限的测试用户
        """
        return cls.create_test_user(
            user_config={'admin': True},
            permissions=["*:*:*"],
            roles=["admin", "test_role"]
        )
    
    @classmethod
    def create_limited_test_user(cls) -> CurrentUserModel:
        """
        创建受限权限的测试用户
        
        Returns:
            CurrentUserModel: 具有受限权限的测试用户
        """
        return cls.create_test_user(
            user_config={
                'user_id': 998,
                'user_name': 'limited_test_user',
                'nick_name': '受限测试用户',
                'admin': False
            },
            role_config={
                'role_id': 998,
                'role_name': '受限测试角色',
                'role_key': 'limited_test_role',
                'data_scope': '2'
            },
            permissions=["system:user:query", "system:role:query"],
            roles=["limited_test_role"]
        )
    
    @classmethod
    def create_dept_test_user(cls, dept_id: int, dept_name: str) -> CurrentUserModel:
        """
        创建特定部门的测试用户
        
        Args:
            dept_id: 部门ID
            dept_name: 部门名称
            
        Returns:
            CurrentUserModel: 属于指定部门的测试用户
        """
        return cls.create_test_user(
            user_config={
                'user_id': 900 + dept_id,
                'user_name': f'dept_{dept_id}_test_user',
                'nick_name': f'{dept_name}测试用户',
                'dept_id': dept_id
            },
            dept_config={
                'dept_id': dept_id,
                'dept_name': dept_name,
                'leader': 900 + dept_id
            }
        )
    
    @classmethod
    def validate_test_user(cls, user: CurrentUserModel) -> bool:
        """
        验证测试用户模型的完整性
        
        Args:
            user: 要验证的用户模型
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 验证基本结构
            assert user is not None, "用户模型不能为空"
            assert isinstance(user, CurrentUserModel), "必须是CurrentUserModel实例"
            
            # 验证用户信息
            assert user.user is not None, "用户信息不能为空"
            assert user.user.user_id is not None, "用户ID不能为空"
            assert user.user.user_name is not None, "用户名不能为空"
            assert user.user.nick_name is not None, "昵称不能为空"
            
            # 验证部门信息
            assert user.user.dept is not None, "部门信息不能为空"
            assert user.user.dept.dept_id is not None, "部门ID不能为空"
            assert user.user.dept.dept_name is not None, "部门名称不能为空"
            
            # 验证角色信息
            assert user.user.role is not None, "角色信息不能为空"
            assert len(user.user.role) > 0, "至少需要一个角色"
            assert user.user.role[0].role_id is not None, "角色ID不能为空"
            assert user.user.role[0].role_name is not None, "角色名称不能为空"
            
            # 验证权限信息
            assert user.permissions is not None, "权限信息不能为空"
            assert len(user.permissions) > 0, "至少需要一个权限"
            assert user.roles is not None, "角色列表不能为空"
            assert len(user.roles) > 0, "至少需要一个角色"
            
            logger.info(f"测试用户验证通过: {user.user.user_name}")
            return True
            
        except AssertionError as e:
            logger.error(f"测试用户验证失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"测试用户验证过程中出错: {str(e)}")
            return False
    
    @classmethod
    def get_supported_test_tokens(cls) -> List[str]:
        """
        获取支持的测试token列表
        
        Returns:
            List[str]: 支持的测试token格式列表
        """
        return ["test_token", "Bearer test_token"]
    
    @classmethod
    def is_test_token(cls, token: str) -> bool:
        """
        检查是否为测试token
        
        Args:
            token: 要检查的token
            
        Returns:
            bool: 是否为测试token
        """
        return token in cls.get_supported_test_tokens()


# 便捷函数
def create_default_test_user() -> CurrentUserModel:
    """
    创建默认测试用户的便捷函数
    
    Returns:
        CurrentUserModel: 默认测试用户
    """
    return TestUserFactory.create_test_user()


def create_admin_test_user() -> CurrentUserModel:
    """
    创建管理员测试用户的便捷函数
    
    Returns:
        CurrentUserModel: 管理员测试用户
    """
    return TestUserFactory.create_admin_test_user()


def validate_test_user(user: CurrentUserModel) -> bool:
    """
    验证测试用户的便捷函数
    
    Args:
        user: 要验证的用户模型
        
    Returns:
        bool: 验证是否通过
    """
    return TestUserFactory.validate_test_user(user)