#!/usr/bin/env python3
import requests
import json
import sys
import os

# 添加项目路径
sys.path.append('/home/<USER>/workspace/lims2/back')

def get_auth_token():
    """获取认证token"""
    base_url = "http://localhost:9099"
    login_endpoint = "/login"
    
    # 尝试登录获取token (使用form data)
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}{login_endpoint}", data=login_data)
        print(f"登录响应状态码: {response.status_code}")
        print(f"登录响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and 'access_token' in data['data']:
                return data['data']['access_token']
            elif 'access_token' in data:
                return data['access_token']
    except Exception as e:
        print(f"登录失败: {e}")
    
    return None

def test_pending_approvals_api():
    """测试待审批项目列表API"""
    base_url = "http://localhost:9099"
    endpoint = "/quotation/project-quotation-approval/pending"
    
    # 获取认证token
    print("=== 获取认证token ===")
    token = get_auth_token()
    if not token:
        print("无法获取有效的认证token，使用测试token")
        token = "test_token"
    else:
        print(f"获取到token: {token[:20]}...")
    
    # 测试基本分页
    print("\n=== 测试基本分页 ===")
    params = {
        "page_num": 1,
        "page_size": 10
    }
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        response = requests.get(f"{base_url}{endpoint}", params=params, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        print()
    except Exception as e:
        print(f"请求失败: {e}")
        return
    
    # 测试查询条件
    print("=== 测试查询条件 ===")
    params_with_filters = {
        "page_num": 1,
        "page_size": 10,
        "project_name": "test",
        "project_code": "P001",
        "customer_name": "客户A",
        "business_type": "检测"
    }
    
    try:
        response = requests.get(f"{base_url}{endpoint}", params=params_with_filters, headers=headers)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data:
                print(f"\n返回数据结构:")
                print(f"- total: {data['data'].get('total', 'N/A')}")
                print(f"- page_num: {data['data'].get('page_num', 'N/A')}")
                print(f"- page_size: {data['data'].get('page_size', 'N/A')}")
                print(f"- pages: {data['data'].get('pages', 'N/A')}")
                print(f"- list length: {len(data['data'].get('list', []))}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    test_pending_approvals_api()