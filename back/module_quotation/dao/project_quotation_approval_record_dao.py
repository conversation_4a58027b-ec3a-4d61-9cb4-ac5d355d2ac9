"""
项目报价审批记录DAO
"""

from sqlalchemy import and_, select, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from config.base_dao import BaseDao
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_quotation.entity.vo.project_quotation_approval_record_vo import (
    ProjectQuotationApprovalRecordPageQueryModel,
    ProjectQuotationApprovalRecordQueryModel
)


class ProjectQuotationApprovalRecordDao(BaseDao[ProjectQuotationApprovalRecord]):
    """
    项目报价审批记录DAO
    """

    def __init__(self, db: AsyncSession):
        """
        初始化

        :param db: 数据库会话
        """
        super().__init__(ProjectQuotationApprovalRecord, db)

    async def get_approval_records_by_quotation_id(self, project_quotation_id: int) -> List[ProjectQuotationApprovalRecord]:
        """
        根据项目报价ID获取审批记录列表

        :param project_quotation_id: 项目报价ID
        :return: 审批记录列表
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        ).order_by(
            ProjectQuotationApprovalRecord.approval_stage,
            ProjectQuotationApprovalRecord.approver_type
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_approval_record_by_user_and_quotation(
        self, 
        project_quotation_id: int, 
        user_id: int
    ) -> Optional[ProjectQuotationApprovalRecord]:
        """
        根据项目报价ID和用户ID获取审批记录

        :param project_quotation_id: 项目报价ID
        :param user_id: 用户ID
        :return: 审批记录
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                ProjectQuotationApprovalRecord.approver_user_id == user_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()

    async def get_pending_approvals_by_user(self, user_id: int) -> List[ProjectQuotationApprovalRecord]:
        """
        获取用户的待审批记录列表

        :param user_id: 用户ID
        :return: 待审批记录列表
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.approver_user_id == user_id,
                ProjectQuotationApprovalRecord.approval_status == "pending",
                ProjectQuotationApprovalRecord.is_required == "1"
            )
        ).order_by(ProjectQuotationApprovalRecord.create_time.desc())
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_approval_records_by_stage(
        self, 
        project_quotation_id: int, 
        stage: int
    ) -> List[ProjectQuotationApprovalRecord]:
        """
        根据项目报价ID和审批阶段获取审批记录

        :param project_quotation_id: 项目报价ID
        :param stage: 审批阶段
        :return: 审批记录列表
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                ProjectQuotationApprovalRecord.approval_stage == stage
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_required_approval_records_by_stage(
        self, 
        project_quotation_id: int, 
        stage: int
    ) -> List[ProjectQuotationApprovalRecord]:
        """
        根据项目报价ID和审批阶段获取必需的审批记录

        :param project_quotation_id: 项目报价ID
        :param stage: 审批阶段
        :return: 必需的审批记录列表
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            and_(
                ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id,
                ProjectQuotationApprovalRecord.approval_stage == stage,
                ProjectQuotationApprovalRecord.is_required == "1"
            )
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def delete_approval_records_by_quotation_id(self, project_quotation_id: int):
        """
        根据项目报价ID删除所有审批记录

        :param project_quotation_id: 项目报价ID
        """
        stmt = select(ProjectQuotationApprovalRecord).where(
            ProjectQuotationApprovalRecord.project_quotation_id == project_quotation_id
        )
        result = await self.db.execute(stmt)
        records = result.scalars().all()
        
        for record in records:
            await self.db.delete(record)

    async def get_approval_records_page(
        self, 
        query_object: ProjectQuotationApprovalRecordPageQueryModel
    ):
        """
        分页查询审批记录

        :param query_object: 查询参数
        :return: 分页结果
        """
        # 构建查询条件
        conditions = []

        if query_object.project_quotation_id:
            conditions.append(ProjectQuotationApprovalRecord.project_quotation_id == query_object.project_quotation_id)
        if query_object.approver_type:
            conditions.append(ProjectQuotationApprovalRecord.approver_type == query_object.approver_type)
        if query_object.approval_status:
            conditions.append(ProjectQuotationApprovalRecord.approval_status == query_object.approval_status)
        if query_object.approver_user_id:
            conditions.append(ProjectQuotationApprovalRecord.approver_user_id == query_object.approver_user_id)
        if query_object.begin_time and query_object.end_time:
            conditions.append(ProjectQuotationApprovalRecord.create_time.between(query_object.begin_time, query_object.end_time))
        elif query_object.begin_time:
            conditions.append(ProjectQuotationApprovalRecord.create_time >= query_object.begin_time)
        elif query_object.end_time:
            conditions.append(ProjectQuotationApprovalRecord.create_time <= query_object.end_time)

        # 执行查询
        stmt = select(ProjectQuotationApprovalRecord).where(and_(*conditions)).order_by(
            ProjectQuotationApprovalRecord.create_time.desc()
        )

        # 计算总数
        count_stmt = select(func.count()).select_from(ProjectQuotationApprovalRecord).where(and_(*conditions))
        total = await self.db.execute(count_stmt)
        total = total.scalar() or 0

        # 分页
        stmt = stmt.offset((query_object.page_num - 1) * query_object.page_size).limit(query_object.page_size)
        result = await self.db.execute(stmt)
        records = result.scalars().all()

        return {"total": total, "rows": records}

    async def get_approval_records_list(
        self, 
        query_object: ProjectQuotationApprovalRecordQueryModel
    ) -> List[ProjectQuotationApprovalRecord]:
        """
        查询审批记录列表

        :param query_object: 查询参数
        :return: 审批记录列表
        """
        # 构建查询条件
        conditions = []

        if query_object.project_quotation_id:
            conditions.append(ProjectQuotationApprovalRecord.project_quotation_id == query_object.project_quotation_id)
        if query_object.approver_type:
            conditions.append(ProjectQuotationApprovalRecord.approver_type == query_object.approver_type)
        if query_object.approval_status:
            conditions.append(ProjectQuotationApprovalRecord.approval_status == query_object.approval_status)
        if query_object.approver_user_id:
            conditions.append(ProjectQuotationApprovalRecord.approver_user_id == query_object.approver_user_id)
        if query_object.begin_time and query_object.end_time:
            conditions.append(ProjectQuotationApprovalRecord.create_time.between(query_object.begin_time, query_object.end_time))
        elif query_object.begin_time:
            conditions.append(ProjectQuotationApprovalRecord.create_time >= query_object.begin_time)
        elif query_object.end_time:
            conditions.append(ProjectQuotationApprovalRecord.create_time <= query_object.end_time)

        # 执行查询
        stmt = select(ProjectQuotationApprovalRecord).where(and_(*conditions)).order_by(
            ProjectQuotationApprovalRecord.create_time.desc()
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()
