"""
项目报价数据模型
"""

from sqlalchemy import Column, Integer, String, DECIMAL, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotation(Base):
    """
    项目报价表
    """

    __tablename__ = "project_quotation"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_name = Column(String(100), nullable=False, comment="项目名称")
    project_code = Column(String(50), nullable=False, unique=True, comment="项目编号")
    contract_code = Column(String(50), nullable=True, comment="合同编号")

    # 客户信息
    customer_id = Column(Integer, nullable=True, comment="客户ID")
    customer_name = Column(String(100), nullable=True, comment="客户名称")
    customer_address = Column(String(200), nullable=True, comment="客户地址")
    customer_contact = Column(String(50), nullable=True, comment="客户联系人")
    customer_phone = Column(String(20), nullable=True, comment="客户联系电话")

    # 受检方信息
    inspected_party = Column(String(100), nullable=True, comment="受检方企业名称")
    inspected_contact = Column(String(50), nullable=True, comment="受检方联系人")
    inspected_phone = Column(String(20), nullable=True, comment="受检方联系电话")
    inspected_address = Column(String(200), nullable=True, comment="受检方详细地址")

    # 项目负责人信息
    project_manager = Column(String(50), nullable=True, comment="项目负责人")
    market_manager = Column(String(50), nullable=True, comment="市场负责人")
    technical_manager = Column(String(50), nullable=True, comment="项目技术人")

    # 委托日期
    commission_date = Column(DateTime, nullable=True, comment="委托日期")

    # 项目审批人已迁移到 ProjectQuotationApprover 关联表

    # 业务类别：sampling-一般采样，sample-送样
    business_type = Column(String(20), nullable=False, default="sampling", comment="业务类别")

    # 项目审批状态：0-草稿，1-待审核，2-已审核，3-已撤回，4-已拒绝
    status = Column(String(1), nullable=False, default="0", comment="项目审批状态")

    # 备注
    remark = Column(Text, nullable=True, comment="备注")

    # 创建人和更新人信息
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, comment="更新时间")

    # 关联项目报价明细
    items = relationship("ProjectQuotationItem", back_populates="project_quotation", cascade="all, delete-orphan")

    # 关联项目报价附件
    attachments = relationship(
        "ProjectQuotationAttachment", back_populates="project_quotation", cascade="all, delete-orphan"
    )

    # 关联项目报价其他费用
    other_fees = relationship(
        "ProjectQuotationOtherFee", back_populates="project_quotation", cascade="all, delete-orphan"
    )

    # 关联项目报价总费用
    total_fee = relationship(
        "ProjectQuotationTotalFee", back_populates="project_quotation", cascade="all, delete-orphan", uselist=False
    )

    # 关联项目报价审核人
    approver_relations = relationship(
        "ProjectQuotationApprover", back_populates="project_quotation", cascade="all, delete-orphan"
    )
