"""项目报价审核人关联表数据模型"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, BigInteger
from sqlalchemy.orm import relationship

from config.database import Base


class ProjectQuotationApprover(Base):
    """
    项目报价审核人关联表
    """

    __tablename__ = "project_quotation_approver"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="主键ID")
    project_quotation_id = Column(Integer, ForeignKey('project_quotation.id'), nullable=False, comment="项目报价ID")
    user_id = Column(BigInteger, ForeignKey('sys_user.user_id'), nullable=False, comment="审核人用户ID")
    create_by = Column(String(50), nullable=True, comment="创建人")
    create_time = Column(DateTime, nullable=True, default=datetime.now, comment="创建时间")
    update_by = Column(String(50), nullable=True, comment="更新人")
    update_time = Column(DateTime, nullable=True, default=datetime.now, comment="更新时间")

    # 关联项目报价表
    project_quotation = relationship("ProjectQuotation", back_populates="approver_relations")
    
    # 关联系统用户表
    user = relationship("SysUser", foreign_keys=[user_id])