"""
项目报价审批记录VO模型
"""

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import Optional, List
from module_admin.annotation.pydantic_annotation import as_query


class ProjectQuotationApprovalRecordModel(BaseModel):
    """
    项目报价审批记录对应pydantic模型
    """

    model_config = ConfigDict(alias_generator=to_camel, from_attributes=True)

    id: Optional[int] = Field(default=None, description="主键ID")
    project_quotation_id: Optional[int] = Field(default=None, description="项目报价ID")
    approver_type: Optional[str] = Field(default=None, description="审批人类型")
    approver_user_id: Optional[int] = Field(default=None, description="审批人用户ID")
    approval_status: Optional[str] = Field(default="pending", description="审批状态")
    approval_opinion: Optional[str] = Field(default=None, description="审批意见")
    approval_time: Optional[datetime] = Field(default=None, description="审批时间")
    approval_order: Optional[int] = Field(default=None, description="审批顺序")
    create_by: Optional[int] = Field(default=None, description="创建人")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[int] = Field(default=None, description="更新人")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")


class ProjectQuotationApprovalRecordQueryModel(ProjectQuotationApprovalRecordModel):
    """
    项目报价审批记录查询模型
    """

    begin_time: Optional[str] = Field(default=None, description="开始时间")
    end_time: Optional[str] = Field(default=None, description="结束时间")


@as_query
class ProjectQuotationApprovalRecordPageQueryModel(ProjectQuotationApprovalRecordQueryModel):
    """
    项目报价审批记录分页查询模型
    """

    page_num: int = Field(default=1, description="当前页码")
    page_size: int = Field(default=10, description="每页记录数")


class AddProjectQuotationApprovalRecordModel(ProjectQuotationApprovalRecordModel):
    """
    新增项目报价审批记录模型
    """

    project_quotation_id: int = Field(description="项目报价ID")
    approver_type: str = Field(description="审批人类型")
    approver_user_id: int = Field(description="审批人用户ID")
    approval_order: int = Field(description="审批顺序")


class EditProjectQuotationApprovalRecordModel(AddProjectQuotationApprovalRecordModel):
    """
    编辑项目报价审批记录模型
    """

    id: int = Field(description="主键ID")


class DeleteProjectQuotationApprovalRecordModel(BaseModel):
    """
    删除项目报价审批记录模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    ids: str = Field(description="需要删除的审批记录ID")


class ApprovalActionModel(BaseModel):
    """
    审批操作模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    project_quotation_id: int = Field(description="项目报价ID")
    approval_status: str = Field(description="审批状态：approved-通过，rejected-拒绝")
    approval_opinion: Optional[str] = Field(default=None, description="审批意见")


class ApprovalStatusModel(BaseModel):
    """
    审批状态模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    approver_type: str = Field(description="审批人类型")
    approval_status: str = Field(description="审批状态")
    approver_name: Optional[str] = Field(default=None, description="审批人姓名")
    approval_time: Optional[datetime] = Field(default=None, description="审批时间")
    approval_opinion: Optional[str] = Field(default=None, description="审批意见")


class ProjectQuotationApprovalStatusModel(BaseModel):
    """
    项目报价审批状态模型
    """

    model_config = ConfigDict(alias_generator=to_camel)

    project_quotation_id: int = Field(description="项目报价ID")
    business_type: str = Field(description="业务类别")
    overall_status: str = Field(description="整体审批状态")
    approval_records: List[ApprovalStatusModel] = Field(description="审批记录列表")
