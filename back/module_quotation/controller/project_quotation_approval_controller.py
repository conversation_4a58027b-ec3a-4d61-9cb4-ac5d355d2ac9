"""
项目报价审批控制器
"""

from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession

from config.get_db import get_db
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.login_service import LoginService
from module_quotation.entity.vo.project_quotation_approval_record_vo import (
    ApprovalActionModel,
    ProjectQuotationApprovalStatusModel
)
from module_quotation.entity.vo.project_quotation_vo import ProjectQuotationPageQueryModel
from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
from utils.response_util import ResponseUtil
from utils.log_util import logger

router = APIRouter(
    prefix="/quotation/project-quotation-approval",
    tags=["项目报价审批管理"],
)


@router.get("/status/{project_quotation_id}", response_model=ProjectQuotationApprovalStatusModel)
async def get_approval_status(
    request: Request,
    project_quotation_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取项目报价审批状态
    """
    try:
        approval_service = ProjectQuotationApprovalService(db)
        approval_status = await approval_service.get_approval_status(project_quotation_id)

        logger.info(f"获取项目报价审批状态成功，项目ID：{project_quotation_id}")
        return ResponseUtil.success(data=approval_status)
    except Exception as e:
        logger.error(f"获取项目报价审批状态失败：{str(e)}")
        return ResponseUtil.error(message=f"获取审批状态失败：{str(e)}")


@router.post("/submit/{project_quotation_id}")
async def submit_for_approval(
    request: Request,
    project_quotation_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    提交审批
    """
    try:
        approval_service = ProjectQuotationApprovalService(db)
        await approval_service.submit_for_approval(project_quotation_id, current_user)

        logger.info(f"提交审批成功，项目ID：{project_quotation_id}")
        return ResponseUtil.success(message="提交审批成功")
    except Exception as e:
        logger.error(f"提交审批失败：{str(e)}")
        return ResponseUtil.error(message=f"提交审批失败：{str(e)}")


@router.post("/approve/{project_quotation_id}")
async def perform_approval(
    request: Request,
    project_quotation_id: int,
    approval_action: ApprovalActionModel,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    执行审批操作
    """
    try:
        approval_service = ProjectQuotationApprovalService(db)
        await approval_service.perform_approval(project_quotation_id, approval_action, current_user)

        action_text = "通过" if approval_action.approval_status == "approved" else "拒绝"
        logger.info(f"审批操作成功，项目ID：{project_quotation_id}，操作：{action_text}")
        return ResponseUtil.success(message=f"审批{action_text}成功")
    except Exception as e:
        logger.error(f"审批操作失败：{str(e)}")
        return ResponseUtil.error(message=f"审批操作失败：{str(e)}")


@router.get("/pending")
async def get_pending_approvals(
    request: Request,
    query_params: ProjectQuotationPageQueryModel = Depends(),
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    获取当前用户待审批的项目列表（分页）
    
    :param request: 请求对象
    :param query_params: 查询参数（支持驼峰命名）
    :param db: 数据库会话
    :param current_user: 当前用户
    :return: 分页查询结果
    """
    try:
        approval_service = ProjectQuotationApprovalService(db)
        
        # 将Pydantic模型转换为字典，保持与服务层接口一致
        query_dict = {
            'page_num': query_params.page_num,
            'page_size': query_params.page_size,
            'project_name': query_params.project_name,
            'project_code': query_params.project_code,
            'customer_name': query_params.customer_name,
            'business_type': query_params.business_type
        }
        
        result = await approval_service.get_pending_approvals_paginated(current_user, query_dict)

        logger.info(f"获取待审批项目列表成功，用户：{current_user.user.user_name}，页码：{query_params.page_num}，每页：{query_params.page_size}")
        return ResponseUtil.success(data=result)
    except Exception as e:
        logger.error(f"获取待审批项目列表失败：{str(e)}")
        return ResponseUtil.error(msg=f"获取待审批项目列表失败：{str(e)}")


@router.post("/init/{project_quotation_id}")
async def init_approval_records(
    request: Request,
    project_quotation_id: int,
    business_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user)
):
    """
    初始化审批记录
    """
    try:
        approval_service = ProjectQuotationApprovalService(db)
        await approval_service.init_approval_records(project_quotation_id, business_type, current_user)

        logger.info(f"初始化审批记录成功，项目ID：{project_quotation_id}")
        return ResponseUtil.success(message="初始化审批记录成功")
    except Exception as e:
        logger.error(f"初始化审批记录失败：{str(e)}")
        return ResponseUtil.error(message=f"初始化审批记录失败：{str(e)}")
