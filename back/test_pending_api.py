#!/usr/bin/env python3
"""
测试待审批接口
"""

import requests
import json

def test_pending_api():
    """测试待审批接口"""
    url = "http://localhost:9099/quotation/project-quotation-approval/pending"
    
    # 测试不同的认证方式
    test_cases = [
        {"name": "无认证", "headers": {}},
        {"name": "Bearer token", "headers": {"Authorization": "Bearer test_token"}},
        {"name": "Basic auth", "headers": {"Authorization": "Basic dGVzdDp0ZXN0"}},
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        try:
            response = requests.get(url, headers=case['headers'], timeout=5)
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.text:
                try:
                    data = response.json()
                    print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                except:
                    print(f"响应文本: {response.text}")
            else:
                print("无响应内容")
                
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
        except Exception as e:
            print(f"其他错误: {e}")

if __name__ == "__main__":
    test_pending_api()