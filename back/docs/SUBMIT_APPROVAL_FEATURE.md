# 项目报价提交审批功能说明

## 📋 功能概述

已成功为项目报价列表添加了提交审批功能，用户可以将草稿状态的项目提交到审批流程中。

## ✅ 已实现的功能

### 1. 前端界面
- **提交审批按钮**: 在项目报价列表的操作列中添加了绿色的"提交审批"按钮
- **显示条件**: 只有草稿状态(status='0')的项目才显示此按钮
- **权限控制**: 只有项目创建人或管理员可以提交审批
- **业务类型选择**: 提交时弹出对话框选择业务类型

### 2. 业务类型选择对话框
- **一般采样(sampling)**: 需要市场→实验室→现场三级审批
- **送样(sample)**: 需要市场→实验室二级审批（现场审批可选）
- **用户友好**: 清晰显示不同业务类型的审批流程说明

### 3. 后端API
- **API端点**: `POST /quotation/project-quotation/{id}/submit-approval`
- **参数**: `business_type` (sampling/sample)
- **功能**: 
  - 初始化审批记录
  - 提交审批流程
  - 更新项目状态为待审核
  - 设置业务类型

### 4. 数据库更新
- **business_type字段**: 已添加到project_quotation表
- **审批记录表**: project_quotation_approval_record表已就绪
- **索引优化**: 为相关字段添加了索引

## 🔧 技术实现

### 前端文件修改
1. **`front/src/views/quotation/project-quotation/index.vue`**
   - 添加提交审批按钮
   - 添加业务类型选择对话框
   - 添加相关方法和状态管理

2. **`front/src/api/quotation/projectQuotation.js`**
   - 添加`submitProjectQuotationApproval`API方法

### 后端文件修改
1. **`back/module_quotation/controller/project_quotation_controller.py`**
   - 添加`submit_project_quotation_approval`端点

2. **`back/module_quotation/service/project_quotation_service.py`**
   - 添加`update_project_quotation_business_type`方法

3. **数据库迁移脚本**
   - `back/scripts/migrate_business_type.py`
   - `back/scripts/migrate_all_missing_fields.py`

## 📱 使用流程

### 用户操作步骤
1. **查看项目列表**: 在项目报价列表页面查看项目
2. **点击提交审批**: 对草稿状态的项目点击绿色"提交审批"按钮
3. **确认提交**: 在确认对话框中点击"确定提交"
4. **选择业务类型**: 在弹出的对话框中选择业务类型
5. **完成提交**: 点击"确认提交"完成操作

### 系统处理流程
1. **验证权限**: 检查用户是否有权限提交该项目
2. **验证状态**: 确认项目为草稿状态
3. **初始化审批**: 根据业务类型创建审批记录
4. **启动流程**: 将项目提交到审批流程
5. **更新状态**: 项目状态变为"待审核"
6. **通知用户**: 显示成功消息

## 🎯 审批流程

### 一般采样项目 (sampling)
```
草稿 → 市场审批 → 实验室审批 → 现场审批 → 已审核
```

### 送样项目 (sample)
```
草稿 → 市场审批 → 实验室审批 → 已审核
                              ↓
                         现场审批(可选)
```

## 🔒 权限控制

- **提交权限**: 项目创建人或管理员
- **状态限制**: 只有草稿状态的项目可以提交
- **业务验证**: 确保项目信息完整

## 🚀 后续功能

该功能为审批流程的起点，后续还需要实现：
1. **审批人员界面**: 市场、实验室、现场审批人员的审批界面
2. **审批历史**: 查看项目的审批历史记录
3. **状态跟踪**: 实时跟踪项目审批进度
4. **通知系统**: 审批状态变更通知
5. **撤回功能**: 允许撤回已提交的审批

## 📊 状态说明

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 0 | 草稿 | 项目创建后的初始状态，可以提交审批 |
| 1 | 待审核 | 已提交审批，等待审批人员处理 |
| 2 | 已审核 | 所有必需的审批都已通过 |
| 3 | 已撤回 | 项目已被撤回，可重新提交 |
| 4 | 已拒绝 | 审批被拒绝，需要修改后重新提交 |

## 🎉 总结

提交审批功能已完全实现并测试通过，用户现在可以：
- 在项目报价列表中看到提交审批按钮
- 选择合适的业务类型提交审批
- 项目自动进入审批流程
- 状态实时更新为待审核

该功能为完整的项目报价审批系统奠定了基础，后续可以继续开发审批人员的操作界面。
