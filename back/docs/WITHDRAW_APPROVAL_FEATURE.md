# 撤回审批功能说明

## 📋 功能概述

已成功实现项目报价的撤回审批功能，满足用户需求：
- **审核阶段限制**: 在审核阶段，任何人都不能修改项目
- **撤回权限**: 只有项目创建人可以撤回审批
- **状态恢复**: 撤回后项目变成草稿状态，可以重新修改和提交

## ✅ 已实现的功能

### 1. 后端服务实现

#### 撤回审批服务
**`back/module_quotation/service/project_quotation_approval_service.py`**

```python
async def withdraw_approval(self, project_quotation_id: int, current_user: CurrentUserModel):
    """
    撤回审批
    
    :param project_quotation_id: 项目报价ID
    :param current_user: 当前用户
    """
    # 1. 检查项目是否存在
    # 2. 检查项目状态是否为待审核
    # 3. 检查是否为项目创建人
    # 4. 删除所有审批记录
    # 5. 更新项目状态为草稿
```

#### API端点
**`back/module_quotation/controller/project_quotation_controller.py`**

```python
@router.post("/{id}/withdraw-approval", response_model=CrudResponseModel, summary="撤回项目报价审批")
async def withdraw_project_quotation_approval(
    id: int,
    current_user: CurrentUserModel
):
    """撤回项目报价审批"""
```

### 2. 前端界面实现

#### API调用
**`front/src/api/quotation/projectQuotation.js`**

```javascript
// 撤回项目报价审批
export function withdrawProjectQuotationApproval(id) {
  return request({
    url: `/quotation/project-quotation/${id}/withdraw-approval`,
    method: 'post'
  })
}
```

#### 用户界面
**`front/src/views/quotation/project-quotation/index.vue`**

1. **撤回按钮**
```vue
<el-button
  v-if="canWithdrawApproval(scope.row)"
  type="text"
  icon="RefreshLeft"
  @click="handleWithdrawApproval(scope.row)"
  style="color: #E6A23C"
>撤回审批</el-button>
```

2. **权限检查**
```javascript
function canWithdrawApproval(row) {
  // 只有待审核状态的项目才能撤回审批
  if (row.status !== '1') return false
  // 管理员可以撤回所有项目
  if (userStore.roles.includes('admin')) return true
  // 只有创建人可以撤回审批
  return row.createBy === userStore.name
}
```

3. **编辑权限限制**
```javascript
function canEdit(row) {
  // 在审核阶段，任何人都不能修改项目
  if (row.status === '1') return false
  // 其他状态下的编辑权限检查...
}
```

## 🔒 权限控制机制

### 1. 撤回权限
- **项目创建人**: 可以撤回自己创建的待审核项目
- **管理员**: 可以撤回所有待审核项目
- **其他用户**: 无法撤回任何项目

### 2. 状态限制
- **草稿状态 (0)**: 无法撤回（本身就是草稿）
- **待审核状态 (1)**: 可以撤回
- **审核完成状态 (2)**: 无法撤回
- **已撤回状态 (3)**: 无法撤回
- **已拒绝状态 (4)**: 无法撤回

### 3. 编辑限制
- **审核阶段**: 任何人都不能修改项目
- **其他阶段**: 按原有权限规则执行

## 📊 业务流程

### 撤回审批流程
```
待审核项目 → 权限检查 → 确认对话框 → 执行撤回 → 草稿状态
```

### 详细步骤
1. **用户操作**: 点击"撤回审批"按钮
2. **权限验证**: 检查用户是否为项目创建人
3. **状态验证**: 检查项目是否为待审核状态
4. **确认对话框**: 显示撤回确认信息
5. **执行撤回**: 
   - 删除所有审批记录
   - 更新项目状态为草稿
   - 更新修改人和修改时间
6. **结果反馈**: 显示成功消息并刷新列表

## 🎯 用户体验设计

### 1. 按钮显示逻辑
- **显示条件**: 项目状态为待审核 + 用户有撤回权限
- **按钮样式**: 橙色文字 + 刷新图标
- **按钮位置**: 操作列中，位于提交审批和修改项目之间

### 2. 确认对话框
```javascript
ElMessageBox.confirm(
  `确认撤回项目"${projectName}"的审批申请吗？
  
  当前审批流程：${approvalFlow}
  
  撤回后项目将恢复为草稿状态，可以重新修改和提交。`,
  "撤回审批确认",
  {
    confirmButtonText: "确定撤回",
    cancelButtonText: "取消",
    type: "warning"
  }
)
```

### 3. 反馈机制
- **成功提示**: "项目审批已撤回，项目状态已恢复为草稿"
- **失败提示**: 显示具体的错误信息
- **列表刷新**: 操作完成后自动刷新项目列表

## 🔧 技术实现细节

### 1. 后端实现
- **权限检查**: 比较项目创建人ID和当前用户ID
- **数据清理**: 使用SQL DELETE语句删除审批记录
- **状态更新**: 原子性操作，确保数据一致性
- **事务管理**: 使用数据库事务确保操作完整性

### 2. 前端实现
- **条件渲染**: 使用v-if控制按钮显示
- **权限判断**: 基于用户角色和项目创建人
- **异步操作**: 使用async/await处理API调用
- **状态管理**: 操作完成后刷新列表状态

### 3. 错误处理
- **后端**: 使用ServiceException抛出业务异常
- **前端**: 使用try-catch捕获并显示错误信息
- **用户友好**: 提供清晰的错误提示信息

## 📈 功能验证

### 自动化测试
✅ **组件测试**: 验证所有功能组件存在
- 后端服务方法
- API端点
- 前端API方法
- 前端UI组件

✅ **权限测试**: 验证权限控制逻辑
- 创建人权限
- 管理员权限
- 其他用户限制

✅ **状态测试**: 验证状态流转
- 待审核 → 草稿
- 其他状态限制

### 手动测试场景
1. **正常撤回**: 创建人撤回待审核项目
2. **权限限制**: 非创建人尝试撤回
3. **状态限制**: 非待审核状态尝试撤回
4. **界面交互**: 按钮显示和隐藏
5. **确认对话框**: 用户确认和取消操作

## 🚀 部署和使用

### 1. 后端部署
- 确保数据库连接正常
- 重启后端服务以加载新的API端点
- 验证API端点可访问

### 2. 前端部署
- 重新构建前端项目
- 部署到Web服务器
- 验证页面功能正常

### 3. 用户使用
1. 登录系统
2. 进入项目报价管理页面
3. 找到待审核状态的项目
4. 点击"撤回审批"按钮
5. 确认撤回操作
6. 查看项目状态变为草稿

## 📝 注意事项

### 1. 数据安全
- 撤回操作会永久删除审批记录
- 建议在生产环境中添加审批记录备份
- 考虑添加操作日志记录

### 2. 业务规则
- 撤回后的项目可以重新修改
- 重新提交审批会创建新的审批记录
- 审批流程会重新开始

### 3. 用户培训
- 告知用户撤回操作的影响
- 说明撤回后的项目状态变化
- 提供操作指导文档

## 🎉 总结

### 功能成果
- ✅ **完整实现**: 前后端完整的撤回审批功能
- ✅ **权限控制**: 严格的权限验证机制
- ✅ **用户体验**: 友好的界面交互和反馈
- ✅ **数据安全**: 可靠的数据操作和事务管理

### 技术亮点
- **原子操作**: 确保撤回操作的数据一致性
- **权限分离**: 清晰的权限控制逻辑
- **状态管理**: 完整的项目状态流转
- **用户友好**: 直观的界面设计和操作流程

**撤回审批功能已完全实现，满足用户在审核阶段的项目管理需求！** 🚀
