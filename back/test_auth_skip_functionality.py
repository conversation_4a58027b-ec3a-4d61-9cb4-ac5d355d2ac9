#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试认证跳过功能

验证重构后的测试环境认证跳过功能是否正常工作
"""

import requests
import json
from typing import Dict, Any

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
TEST_ENDPOINTS = [
    "/dev-api/system/user/profile",
    "/dev-api/system/menu/list",
    "/dev-api/system/role/list",
    "/dev-api/system/dept/list"
]

def test_auth_skip_with_token(token: str, description: str) -> Dict[str, Any]:
    """
    测试使用指定token的认证跳过功能
    
    Args:
        token: 测试token
        description: 测试描述
        
    Returns:
        Dict[str, Any]: 测试结果
    """
    print(f"\n=== {description} ===")
    print(f"使用Token: {token}")
    
    headers = {
        "Authorization": token,
        "Content-Type": "application/json"
    }
    
    results = []
    
    for endpoint in TEST_ENDPOINTS:
        try:
            url = f"{BASE_URL}{endpoint}"
            print(f"\n测试接口: {endpoint}")
            
            response = requests.get(url, headers=headers, timeout=10)
            
            result = {
                "endpoint": endpoint,
                "status_code": response.status_code,
                "success": response.status_code == 200,
                "response_size": len(response.text)
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result["response_code"] = data.get("code", "N/A")
                    result["response_msg"] = data.get("msg", "N/A")
                    print(f"  ✅ 状态码: {response.status_code}")
                    print(f"  📝 响应码: {result['response_code']}")
                    print(f"  📝 响应消息: {result['response_msg']}")
                except json.JSONDecodeError:
                    result["response_code"] = "JSON解析失败"
                    result["response_msg"] = "响应不是有效的JSON格式"
                    print(f"  ⚠️  状态码: {response.status_code} (JSON解析失败)")
            else:
                print(f"  ❌ 状态码: {response.status_code}")
                print(f"  📝 响应内容: {response.text[:200]}...")
                result["error_detail"] = response.text[:200]
            
            results.append(result)
            
        except requests.exceptions.RequestException as e:
            error_result = {
                "endpoint": endpoint,
                "status_code": "请求异常",
                "success": False,
                "error": str(e)
            }
            results.append(error_result)
            print(f"  ❌ 请求异常: {str(e)}")
        except Exception as e:
            error_result = {
                "endpoint": endpoint,
                "status_code": "未知错误",
                "success": False,
                "error": str(e)
            }
            results.append(error_result)
            print(f"  ❌ 未知错误: {str(e)}")
    
    return {
        "token": token,
        "description": description,
        "results": results,
        "success_count": sum(1 for r in results if r.get("success", False)),
        "total_count": len(results)
    }

def test_auth_skip_without_token() -> Dict[str, Any]:
    """
    测试不使用token的情况（应该返回401）
    
    Returns:
        Dict[str, Any]: 测试结果
    """
    print(f"\n=== 测试无Token访问（预期401） ===")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    results = []
    
    for endpoint in TEST_ENDPOINTS[:2]:  # 只测试前两个接口
        try:
            url = f"{BASE_URL}{endpoint}"
            print(f"\n测试接口: {endpoint}")
            
            response = requests.get(url, headers=headers, timeout=10)
            
            result = {
                "endpoint": endpoint,
                "status_code": response.status_code,
                "success": response.status_code == 401,  # 预期401
                "response_size": len(response.text)
            }
            
            if response.status_code == 401:
                print(f"  ✅ 状态码: {response.status_code} (符合预期)")
            else:
                print(f"  ⚠️  状态码: {response.status_code} (预期401)")
                print(f"  📝 响应内容: {response.text[:200]}...")
            
            results.append(result)
            
        except Exception as e:
            error_result = {
                "endpoint": endpoint,
                "status_code": "异常",
                "success": False,
                "error": str(e)
            }
            results.append(error_result)
            print(f"  ❌ 异常: {str(e)}")
    
    return {
        "description": "无Token访问测试",
        "results": results,
        "success_count": sum(1 for r in results if r.get("success", False)),
        "total_count": len(results)
    }

def main():
    """
    主测试函数
    """
    print("🚀 开始测试认证跳过功能")
    print(f"📍 测试服务器: {BASE_URL}")
    print(f"📋 测试接口数量: {len(TEST_ENDPOINTS)}")
    
    # 测试用例
    test_cases = [
        ("test_token", "测试Token (test_token)"),
        ("Bearer test_token", "测试Token (Bearer test_token)"),
        ("invalid_token", "无效Token测试 (预期401)")
    ]
    
    all_results = []
    
    # 执行测试用例
    for token, description in test_cases:
        result = test_auth_skip_with_token(token, description)
        all_results.append(result)
    
    # 测试无token情况
    no_token_result = test_auth_skip_without_token()
    all_results.append(no_token_result)
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    total_success = 0
    total_tests = 0
    
    for result in all_results:
        description = result.get("description", "未知测试")
        success_count = result.get("success_count", 0)
        total_count = result.get("total_count", 0)
        
        total_success += success_count
        total_tests += total_count
        
        status_icon = "✅" if success_count == total_count else "⚠️"
        print(f"{status_icon} {description}: {success_count}/{total_count} 成功")
        
        # 显示失败的接口
        if success_count < total_count:
            failed_endpoints = [
                r["endpoint"] for r in result.get("results", [])
                if not r.get("success", False)
            ]
            if failed_endpoints:
                print(f"   失败接口: {', '.join(failed_endpoints)}")
    
    print(f"\n🎯 总体结果: {total_success}/{total_tests} 测试通过")
    
    # 功能验证结论
    test_token_results = [r for r in all_results if "test_token" in r.get("token", "")]
    auth_skip_working = all(r["success_count"] == r["total_count"] for r in test_token_results)
    
    if auth_skip_working:
        print("\n🎉 认证跳过功能正常工作！")
        print("✅ 测试用户工厂重构成功")
        print("✅ 所有测试接口在使用test_token时均可正常访问")
    else:
        print("\n❌ 认证跳过功能存在问题")
        print("🔧 请检查配置和代码实现")
    
    return auth_skip_working

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"\n\n💥 测试过程中发生异常: {str(e)}")
        exit(1)