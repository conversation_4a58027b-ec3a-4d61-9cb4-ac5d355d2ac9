"""
技术手册类目表数据迁移脚本
将现有技术手册中的分类和检测类别数据迁移到技术手册类目表中
"""

import os
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from urllib.parse import quote_plus
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from config.env import DataBaseConfig
from module_basedata.entity.do.technical_manual_category_do import TechnicalManualCategory

# 构建同步数据库连接URL
SQLALCHEMY_DATABASE_URL = (
    f"mysql+pymysql://{DataBaseConfig.db_username}:{quote_plus(DataBaseConfig.db_password)}@"
    f"{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"
)


def migrate_technical_manual_category():
    """
    迁移技术手册类目数据
    """
    # 创建数据库连接
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        print("开始迁移技术手册类目数据...")

        # 1. 创建技术手册类目表（如果不存在）
        print("1. 创建技术手册类目表...")
        TechnicalManualCategory.metadata.create_all(bind=engine)

        # 2. 从技术手册表中提取分类和检测类别数据
        print("2. 提取现有技术手册的分类和检测类别数据...")

        # 查询所有不重复的分类和检测类别组合
        result = db.execute(
            text(
                """
            SELECT DISTINCT classification, category
            FROM technical_manual
            WHERE classification IS NOT NULL
            AND category IS NOT NULL
            ORDER BY classification, category
        """
            )
        )

        category_combinations = result.fetchall()
        print(f"找到 {len(category_combinations)} 个不重复的分类和检测类别组合")

        # 3. 生成类目编号并插入类目表
        print("3. 生成类目编号并插入类目表...")

        category_code_map = {}  # 用于存储 (分类, 检测类别) -> 类目编号 的映射

        for i, (classification, category) in enumerate(category_combinations, 1):
            # 生成类目编号
            category_code = f"CATE{i:05d}"

            # 检查是否已存在
            existing = (
                db.query(TechnicalManualCategory)
                .filter(
                    TechnicalManualCategory.classification == classification,
                    TechnicalManualCategory.category == category,
                )
                .first()
            )

            if not existing:
                # 创建新的类目记录
                new_category = TechnicalManualCategory(
                    category_code=category_code,
                    classification=classification,
                    category=category,
                    status="0",
                    create_by="system",
                    update_by="system",
                    remark="数据迁移自动生成",
                )
                db.add(new_category)
                db.flush()

                category_code_map[(classification, category)] = category_code
                print(f"  创建类目: {category_code} - {classification} - {category}")
            else:
                category_code_map[(classification, category)] = existing.category_code
                print(f"  已存在类目: {existing.category_code} - {classification} - {category}")

        # 4. 更新技术手册表，添加类目编号字段
        print("4. 更新技术手册表结构...")

        # 检查是否已有category_code字段
        result = db.execute(text("SHOW COLUMNS FROM technical_manual LIKE 'category_code'"))
        if not result.fetchone():
            # 添加category_code字段
            db.execute(text("ALTER TABLE technical_manual ADD COLUMN category_code VARCHAR(20) AFTER test_code"))
            print("  添加category_code字段")

        # 检查是否已有special_consumables_price字段
        result = db.execute(text("SHOW COLUMNS FROM technical_manual LIKE 'special_consumables_price'"))
        if not result.fetchone():
            # 添加special_consumables_price字段
            db.execute(
                text(
                    "ALTER TABLE technical_manual ADD COLUMN special_consumables_price DECIMAL(10,2) AFTER has_qualification"
                )
            )
            print("  添加special_consumables_price字段")

        # 5. 更新技术手册记录的类目编号
        print("5. 更新技术手册记录的类目编号...")

        # 直接通过SQL查询更新，因为模型可能还没有这些字段
        updated_count = 0
        for (classification, category), category_code in category_code_map.items():
            # 更新所有匹配的技术手册记录
            result = db.execute(
                text(
                    """
                UPDATE technical_manual
                SET category_code = :category_code
                WHERE classification = :classification
                AND category = :category
            """
                ),
                {"category_code": category_code, "classification": classification, "category": category},
            )
            updated_count += result.rowcount

        print(f"  更新了 {updated_count} 条技术手册记录的类目编号")

        # 6. 迁移特殊耗材单价数据（如果存在的话）
        print("6. 检查特殊耗材单价数据...")

        # 检查技术手册价格表是否有special_consumables_price字段
        try:
            result = db.execute(text("SHOW COLUMNS FROM technical_manual_price LIKE 'special_consumables_price'"))
            if result.fetchone():
                # 如果字段存在，则进行迁移
                result = db.execute(
                    text(
                        """
                    UPDATE technical_manual tm
                    INNER JOIN technical_manual_price tmp ON tm.id = tmp.technical_manual_id
                    SET tm.special_consumables_price = tmp.special_consumables_price
                    WHERE tmp.special_consumables_price IS NOT NULL
                """
                    )
                )
                migrated_price_count = result.rowcount
                print(f"  迁移了 {migrated_price_count} 条特殊耗材单价数据")
            else:
                print("  技术手册价格表中没有special_consumables_price字段，跳过迁移")
                migrated_price_count = 0
        except Exception as e:
            print(f"  迁移特殊耗材单价数据时出错: {str(e)}")
            migrated_price_count = 0

        # 7. 提交事务
        db.commit()
        print("7. 提交事务完成")

        print("技术手册类目数据迁移完成！")
        print(f"总计创建类目: {len(category_combinations)} 个")
        print(f"总计更新技术手册: {updated_count} 条")
        print(f"总计迁移价格数据: {migrated_price_count} 条")

    except Exception as e:
        print(f"迁移过程中发生错误: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


def rollback_migration():
    """
    回滚迁移（仅用于测试）
    """
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        print("开始回滚迁移...")

        # 删除类目表数据
        db.execute(text("DELETE FROM technical_manual_category"))

        # 移除技术手册表的category_code字段
        try:
            db.execute(text("ALTER TABLE technical_manual DROP COLUMN category_code"))
        except:
            pass

        # 移除技术手册表的special_consumables_price字段
        try:
            db.execute(text("ALTER TABLE technical_manual DROP COLUMN special_consumables_price"))
        except:
            pass

        db.commit()
        print("回滚完成")

    except Exception as e:
        print(f"回滚过程中发生错误: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="技术手册类目表数据迁移")
    parser.add_argument("--rollback", action="store_true", help="回滚迁移")

    args = parser.parse_args()

    if args.rollback:
        rollback_migration()
    else:
        migrate_technical_manual_category()
