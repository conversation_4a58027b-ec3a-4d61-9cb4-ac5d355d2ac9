#!/usr/bin/env python3
"""
测试审批修复
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_approval_status_model():
    """测试审批状态模型修复"""
    print("测试审批状态模型...")
    
    try:
        from module_quotation.entity.vo.project_quotation_approval_record_vo import (
            ApprovalStatusModel, 
            ProjectQuotationApprovalStatusModel
        )
        
        # 测试ApprovalStatusModel
        approval_status = ApprovalStatusModel(
            approverType="market",
            approvalStatus="approved",
            approverName="测试审批人",
            approvalTime=datetime.now(),
            approvalOpinion="测试通过"
        )
        print("✅ ApprovalStatusModel 创建成功")
        
        # 测试ProjectQuotationApprovalStatusModel
        project_status = ProjectQuotationApprovalStatusModel(
            projectQuotationId=1,
            businessType="sampling",
            overallStatus="2",
            approvalRecords=[approval_status]
        )
        print("✅ ProjectQuotationApprovalStatusModel 创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 审批状态模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_import():
    """测试服务导入"""
    print("\n测试服务导入...")
    
    try:
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
        print("✅ ProjectQuotationApprovalService 导入成功")
        return True
    except Exception as e:
        print(f"❌ 服务导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_basic_database():
    """测试基本数据库操作"""
    print("\n测试基本数据库操作...")
    
    try:
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.database import Base
        from module_admin.entity.do.user_do import SysUser
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        
        # 创建测试数据库
        TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_fix.db"
        engine = create_async_engine(TEST_DATABASE_URL, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
        
        # 创建表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 数据库表创建成功")
        
        async with TestSessionLocal() as session:
            # 创建用户
            user = SysUser(
                user_id=1,
                user_name="test_user",
                nick_name="测试用户",
                password="test_password",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(user)
            await session.flush()
            
            # 创建项目报价
            quotation = ProjectQuotation(
                id=1,
                project_name="测试项目",
                project_code="TEST001",
                business_type="sampling",
                status="0",
                customer_name="测试客户",
                create_by=user.user_id,
                create_time=datetime.now()
            )
            session.add(quotation)
            await session.flush()
            
            await session.commit()
            print("✅ 基本数据创建成功")
        
        # 清理
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        print("✅ 数据库清理成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("开始审批修复测试\n")
    
    # 测试审批状态模型
    if not await test_approval_status_model():
        print("\n❌ 审批状态模型测试失败")
        return False
    
    # 测试服务导入
    if not await test_service_import():
        print("\n❌ 服务导入测试失败")
        return False
    
    # 测试基本数据库操作
    if not await test_basic_database():
        print("\n❌ 数据库操作测试失败")
        return False
    
    print("\n🎉 所有修复测试都通过了！")
    return True


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
