#!/usr/bin/env python3
"""
最终审批流程测试
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_complete_approval():
    """测试完整审批流程"""
    print("开始完整审批流程测试...")

    try:
        # 导入必要模块
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.database import Base
        from module_admin.entity.do.user_do import SysUser, SysUserRole
        from module_admin.entity.do.role_do import SysRole
        from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.vo.project_quotation_approval_record_vo import ApprovalActionModel
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService

        print("✅ 模块导入成功")

        # 创建数据库
        TEST_DATABASE_URL = "sqlite+aiosqlite:///./test_final.db"
        engine = create_async_engine(TEST_DATABASE_URL, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✅ 数据库创建成功")

        async with TestSessionLocal() as session:
            # 创建角色（不指定ID，让数据库自动生成）
            market_role = SysRole(
                role_name="市场审批人员", role_key="market-approver",
                role_sort=10, status="0", create_by="admin", create_time=datetime.now()
            )
            lab_role = SysRole(
                role_name="实验室审批人员", role_key="lab-approver",
                role_sort=11, status="0", create_by="admin", create_time=datetime.now()
            )
            field_role = SysRole(
                role_name="现场审批人员", role_key="field-approver",
                role_sort=12, status="0", create_by="admin", create_time=datetime.now()
            )
            session.add_all([market_role, lab_role, field_role])
            await session.flush()
            print("✅ 角色创建成功")

            # 创建用户（不指定ID，让数据库自动生成）
            market_user = SysUser(
                user_name="market_user", nick_name="市场用户",
                password="test", status="0", del_flag="0", create_by="admin", create_time=datetime.now()
            )
            lab_user = SysUser(
                user_name="lab_user", nick_name="实验室用户",
                password="test", status="0", del_flag="0", create_by="admin", create_time=datetime.now()
            )
            field_user = SysUser(
                user_name="field_user", nick_name="现场用户",
                password="test", status="0", del_flag="0", create_by="admin", create_time=datetime.now()
            )
            session.add_all([market_user, lab_user, field_user])
            await session.flush()
            print("✅ 用户创建成功")

            # 创建用户角色关联
            user_roles = [
                SysUserRole(user_id=market_user.user_id, role_id=market_role.role_id),
                SysUserRole(user_id=lab_user.user_id, role_id=lab_role.role_id),
                SysUserRole(user_id=field_user.user_id, role_id=field_role.role_id)
            ]
            session.add_all(user_roles)
            await session.flush()
            print("✅ 用户角色关联成功")

            # 创建项目报价（使用时间戳生成唯一编号）
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            quotation = ProjectQuotation(
                project_name="测试审批项目", project_code=f"APPROVAL{timestamp}",
                business_type="sampling", status="0", customer_name="测试客户",
                create_by=market_user.user_id, create_time=datetime.now()
            )
            session.add(quotation)
            await session.flush()
            await session.commit()
            print("✅ 项目报价创建成功")

            # 创建审批服务
            approval_service = ProjectQuotationApprovalService(session)

            # 创建当前用户模型
            market_current_user = CurrentUserModel(
                permissions=[], roles=[],
                user=UserInfoModel(
                    user_id=market_user.user_id, user_name="market_user", nick_name="市场用户",
                    status="0", del_flag="0"
                )
            )

            # 1. 初始化审批记录
            await approval_service.init_approval_records(quotation.id, "sampling", market_current_user)
            print("✅ 审批记录初始化成功")

            # 2. 获取审批状态
            status = await approval_service.get_approval_status(quotation.id)
            print(f"✅ 获取审批状态成功: {status.overallStatus}")
            print(f"   审批记录数量: {len(status.approvalRecords)}")

            # 3. 提交审批
            await approval_service.submit_for_approval(quotation.id, market_current_user)
            print("✅ 提交审批成功")

            # 4. 获取待审批列表
            pending_list = await approval_service.get_pending_approvals(market_current_user)
            print(f"✅ 获取待审批列表成功: {len(pending_list)} 个项目")

            # 5. 市场审批
            market_action = ApprovalActionModel(
                projectQuotationId=quotation.id,
                approvalStatus="approved",
                approvalOpinion="市场审批通过"
            )
            await approval_service.perform_approval(quotation.id, market_action, market_current_user)
            print("✅ 市场审批成功")

            # 6. 实验室审批
            lab_current_user = CurrentUserModel(
                permissions=[], roles=[],
                user=UserInfoModel(
                    user_id=lab_user.user_id, user_name="lab_user", nick_name="实验室用户",
                    status="0", del_flag="0"
                )
            )
            lab_action = ApprovalActionModel(
                projectQuotationId=quotation.id,
                approvalStatus="approved",
                approvalOpinion="实验室审批通过"
            )
            await approval_service.perform_approval(quotation.id, lab_action, lab_current_user)
            print("✅ 实验室审批成功")

            # 7. 现场审批
            field_current_user = CurrentUserModel(
                permissions=[], roles=[],
                user=UserInfoModel(
                    user_id=field_user.user_id, user_name="field_user", nick_name="现场用户",
                    status="0", del_flag="0"
                )
            )
            field_action = ApprovalActionModel(
                projectQuotationId=quotation.id,
                approvalStatus="approved",
                approvalOpinion="现场审批通过"
            )
            await approval_service.perform_approval(quotation.id, field_action, field_current_user)
            print("✅ 现场审批成功")

            # 8. 检查最终状态
            final_status = await approval_service.get_approval_status(quotation.id)
            print(f"✅ 最终审批状态: {final_status.overallStatus}")

            # 验证所有审批都通过
            all_approved = all(record.approvalStatus == "approved" for record in final_status.approvalRecords)
            if all_approved:
                print("✅ 所有审批记录都已通过")
            else:
                print("❌ 部分审批记录未通过")
                return False

        # 清理数据库
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        print("✅ 数据库清理成功")

        return True

    except Exception as e:
        print(f"❌ 审批流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("开始最终审批流程测试\n")

    if await test_complete_approval():
        print("\n🎉 完整审批流程测试通过！")
        print("\n审批流程功能已经完全正常工作：")
        print("  ✅ 审批记录初始化")
        print("  ✅ 审批状态查询")
        print("  ✅ 提交审批")
        print("  ✅ 待审批列表")
        print("  ✅ 市场审批")
        print("  ✅ 实验室审批")
        print("  ✅ 现场审批")
        print("  ✅ 状态计算")
        return True
    else:
        print("\n❌ 审批流程测试失败")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
