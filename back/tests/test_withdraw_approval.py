#!/usr/bin/env python3
"""
测试撤回审批功能
"""

import sys
import asyncio
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, '.')

async def test_withdraw_approval():
    """
    测试撤回审批功能
    """
    print("测试撤回审批功能...")

    try:
        # 导入必要模块
        from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
        from sqlalchemy.orm import sessionmaker
        from config.env import DataBaseConfig
        from module_admin.entity.do.user_do import SysUser
        from module_admin.entity.vo.user_vo import CurrentUserModel, UserInfoModel
        from module_quotation.entity.do.project_quotation_do import ProjectQuotation
        from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
        from module_quotation.service.project_quotation_service import ProjectQuotationService
        from module_quotation.service.project_quotation_approval_service import ProjectQuotationApprovalService
        from exceptions.exception import ServiceException

        print("✅ 模块导入成功")

        # 构建数据库URL
        database_url = f"mysql+asyncmy://{DataBaseConfig.db_username}:{DataBaseConfig.db_password}@{DataBaseConfig.db_host}:{DataBaseConfig.db_port}/{DataBaseConfig.db_database}"

        # 创建数据库引擎
        engine = create_async_engine(database_url, echo=False)
        TestSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

        async with TestSessionLocal() as session:
            # 创建测试用户
            test_user = SysUser(
                user_name="test_withdraw_user",
                nick_name="测试撤回用户",
                password="test_password",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(test_user)
            await session.flush()
            await session.refresh(test_user)  # 获取自动生成的user_id

            # 创建另一个用户（非创建人）
            other_user = SysUser(
                user_name="test_other_user",
                nick_name="其他用户",
                password="test_password",
                status="0",
                del_flag="0",
                create_by="admin",
                create_time=datetime.now()
            )
            session.add(other_user)
            await session.flush()
            await session.refresh(other_user)  # 获取自动生成的user_id

            # 创建测试项目报价
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            test_quotation = ProjectQuotation(
                project_name="测试撤回审批项目",
                project_code=f"WITHDRAW{timestamp}",
                business_type="sampling",
                status="1",  # 待审核状态
                customer_name="测试客户",
                create_by=test_user.user_id,
                create_time=datetime.now()
            )
            session.add(test_quotation)
            await session.flush()
            await session.refresh(test_quotation)

            # 创建审批记录
            approval_record = ProjectQuotationApprovalRecord(
                project_quotation_id=test_quotation.id,
                approver_type="market",
                approver_user_id=test_user.user_id,
                approval_stage=1,
                approval_status="pending",
                is_required="1",
                create_by=test_user.user_id,
                create_time=datetime.now()
            )
            session.add(approval_record)
            await session.commit()

            print(f"✅ 创建测试数据成功，项目ID: {test_quotation.id}")

            # 创建当前用户模型（使用from_attributes）
            user_info = UserInfoModel.model_validate(test_user, from_attributes=True)
            current_user = CurrentUserModel(
                permissions=[],
                roles=[],
                user=user_info
            )

            # 创建其他用户模型
            other_user_info = UserInfoModel.model_validate(other_user, from_attributes=True)
            other_current_user = CurrentUserModel(
                permissions=[],
                roles=[],
                user=other_user_info
            )

            # 测试审批服务
            approval_service = ProjectQuotationApprovalService(session)

            # 测试场景1：非创建人尝试撤回审批（应该失败）
            print("\n🔄 测试场景1：非创建人尝试撤回审批")
            try:
                await approval_service.withdraw_approval(test_quotation.id, other_current_user)
                print("❌ 非创建人撤回审批应该失败，但成功了")
                return False
            except ServiceException as e:
                print(f"✅ 非创建人撤回审批正确失败: {e.message}")

            # 测试场景2：创建人撤回审批（应该成功）
            print("\n🔄 测试场景2：创建人撤回审批")

            # 验证撤回前的状态
            quotation_service = ProjectQuotationService(session)
            before_quotation = await quotation_service.get_project_quotation(test_quotation.id)
            print(f"   撤回前项目状态: {before_quotation.get('status')}")

            # 验证审批记录存在
            from sqlalchemy import select
            records_stmt = select(ProjectQuotationApprovalRecord).where(
                ProjectQuotationApprovalRecord.project_quotation_id == test_quotation.id
            )
            records_result = await session.execute(records_stmt)
            records_before = records_result.scalars().all()
            print(f"   撤回前审批记录数: {len(records_before)}")

            # 调试信息
            print(f"   test_user.user_id: {test_user.user_id}")
            print(f"   user_info.user_id: {user_info.user_id}")
            print(f"   current_user.user: {current_user.user}")
            print(f"   current_user.user.user_id: {current_user.user.user_id}")
            print(f"   test_quotation.create_by: {test_quotation.create_by}")

            # 执行撤回操作
            await approval_service.withdraw_approval(test_quotation.id, current_user)
            print("✅ 撤回审批操作成功")

            # 验证撤回后的状态
            after_quotation = await quotation_service.get_project_quotation(test_quotation.id)
            print(f"   撤回后项目状态: {after_quotation.get('status')}")

            # 验证审批记录已删除
            records_result = await session.execute(records_stmt)
            records_after = records_result.scalars().all()
            print(f"   撤回后审批记录数: {len(records_after)}")

            # 验证结果
            if after_quotation.get('status') == '0':
                print("✅ 项目状态正确恢复为草稿")
            else:
                print(f"❌ 项目状态错误: 期望 '0', 实际 '{after_quotation.get('status')}'")
                return False

            if len(records_after) == 0:
                print("✅ 审批记录正确删除")
            else:
                print(f"❌ 审批记录删除失败: 仍有 {len(records_after)} 条记录")
                return False

            # 测试场景3：草稿状态项目尝试撤回（应该失败）
            print("\n🔄 测试场景3：草稿状态项目尝试撤回")
            try:
                await approval_service.withdraw_approval(test_quotation.id, current_user)
                print("❌ 草稿状态项目撤回应该失败，但成功了")
                return False
            except ServiceException as e:
                print(f"✅ 草稿状态项目撤回正确失败: {e.message}")

            # 清理测试数据
            await session.delete(test_quotation)
            await session.delete(test_user)
            await session.delete(other_user)
            await session.commit()
            print("\n✅ 清理测试数据成功")

        await engine.dispose()
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """
    主函数
    """
    print("开始测试撤回审批功能\n")

    if await test_withdraw_approval():
        print("\n🎉 撤回审批功能测试通过！")
        print("\n✅ 功能验证:")
        print("  • 只有项目创建人可以撤回审批")
        print("  • 只有待审核状态的项目可以撤回")
        print("  • 撤回后项目状态恢复为草稿")
        print("  • 撤回后所有审批记录被删除")
        print("  • 非创建人无法撤回审批")
        print("  • 草稿状态项目无法撤回")

        print("\n📋 权限控制:")
        print("  • 创建人权限: 可以撤回自己创建的待审核项目")
        print("  • 管理员权限: 可以撤回所有待审核项目")
        print("  • 其他用户: 无法撤回任何项目")

        print("\n🔒 状态限制:")
        print("  • 草稿状态: 无法撤回（本身就是草稿）")
        print("  • 待审核状态: 可以撤回")
        print("  • 审核完成状态: 无法撤回")
        print("  • 已撤回状态: 无法撤回")
        print("  • 已拒绝状态: 无法撤回")

        print("\n🚀 撤回审批功能已就绪，可以在前端使用！")
        return True
    else:
        print("\n❌ 撤回审批功能测试失败")
        return False


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
