#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
待审批项目分页接口测试
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime

from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_approval_record_do import ProjectQuotationApprovalRecord
from module_admin.entity.do.user_do import SysUser
from module_admin.entity.do.role_do import SysRole
from module_admin.entity.do.user_do import SysUserRole


class TestPendingApprovalPagination:
    """待审批项目分页接口测试类"""

    @pytest.fixture
    async def setup_test_data(self, db_session: AsyncSession):
        """
        设置测试数据
        """
        # 创建测试用户
        test_user = SysUser(
            user_id=999,
            user_name="test_approver",
            nick_name="测试审批人",
            email="<EMAIL>",
            phonenumber="13800138000",
            sex="0",
            status="0",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(test_user)
        
        # 创建测试角色
        test_role = SysRole(
            role_id=999,
            role_name="market-approver",
            role_key="market-approver",
            role_sort=1,
            status="0",
            create_by=1,
            create_time=datetime.now()
        )
        db_session.add(test_role)
        
        # 创建用户角色关联
        user_role = SysUserRole(
            user_id=999,
            role_id=999
        )
        db_session.add(user_role)
        
        # 创建测试项目报价数据
        test_quotations = []
        for i in range(25):  # 创建25条数据用于测试分页
            quotation = ProjectQuotation(
                project_name=f"测试项目{i+1}",
                project_code=f"TEST{i+1:03d}",
                business_type="sampling" if i % 2 == 0 else "sample",
                customer_name=f"测试客户{i+1}",
                status="1",  # 待审核状态
                create_by=1,
                create_time=datetime.now()
            )
            db_session.add(quotation)
            test_quotations.append(quotation)
        
        await db_session.commit()
        
        # 为每个项目创建审批记录
        for quotation in test_quotations:
            approval_record = ProjectQuotationApprovalRecord(
                project_quotation_id=quotation.id,
                approver_type="market",
                approver_user_id=999,
                approval_stage=1,
                approval_status="pending",
                is_required="1",
                create_by=1,
                create_time=datetime.now()
            )
            db_session.add(approval_record)
        
        await db_session.commit()
        
        return test_quotations

    async def test_get_pending_approvals_pagination_default(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试默认分页参数
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        assert "list" in result
        assert "total" in result
        assert "pageNum" in result
        assert "pageSize" in result
        assert "pages" in result
        
        # 默认分页参数：第1页，每页10条
        assert result["pageNum"] == 1
        assert result["pageSize"] == 10
        assert len(result["list"]) <= 10

    async def test_get_pending_approvals_pagination_custom(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试自定义分页参数
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            params={"pageNum": 2, "pageSize": 5},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        assert result["pageNum"] == 2
        assert result["pageSize"] == 5
        assert len(result["list"]) <= 5

    async def test_get_pending_approvals_search_by_project_name(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试按项目名称搜索
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            params={"projectName": "测试项目1"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        # 应该找到包含"测试项目1"的项目（测试项目1, 测试项目10-19）
        assert result["total"] > 0
        for item in result["list"]:
            assert "测试项目1" in item["projectName"]

    async def test_get_pending_approvals_search_by_business_type(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试按业务类别搜索
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            params={"businessType": "sampling"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        assert result["total"] > 0
        for item in result["list"]:
            assert item["businessType"] == "sampling"

    async def test_get_pending_approvals_search_combined(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试组合搜索条件
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            params={
                "projectName": "测试项目",
                "businessType": "sampling",
                "pageNum": 1,
                "pageSize": 5
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        assert result["pageNum"] == 1
        assert result["pageSize"] == 5
        assert len(result["list"]) <= 5
        
        for item in result["list"]:
            assert "测试项目" in item["projectName"]
            assert item["businessType"] == "sampling"

    async def test_get_pending_approvals_empty_result(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试空结果
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            params={"projectName": "不存在的项目"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        assert result["total"] == 0
        assert len(result["list"]) == 0
        assert result["pages"] == 0

    async def test_get_pending_approvals_invalid_page(self, client: AsyncClient, setup_test_data, auth_headers):
        """
        测试无效页码
        """
        response = await client.get(
            "/dev-api/quotation/project-quotation-approval/pending",
            params={"pageNum": 999, "pageSize": 10},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 200
        
        result = data["data"]
        assert result["pageNum"] == 999
        assert len(result["list"]) == 0  # 超出范围的页码应该返回空列表