<template>
  <el-dialog
    title="报价审批"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    @close="handleClose"
  >
    <div v-if="quotation">
      <!-- 项目基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span>项目基本信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目编号：</span>
              <span>{{ quotation.projectCode }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目名称：</span>
              <span>{{ quotation.projectName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">业务类别：</span>
              <el-tag :type="quotation.businessType === 'sampling' ? 'primary' : 'success'">
                {{ quotation.businessType === 'sampling' ? '一般采样' : '送样' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">客户名称：</span>
              <span>{{ quotation.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目负责人：</span>
              <span>{{ quotation.projectManager }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">当前状态：</span>
              <el-tag
                :type="getStatusTagType(currentDetailedStatus || quotation.status)"
                size="small"
              >
                {{ currentDetailedStatusLabel || getStatusLabel(quotation.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审批历史记录 -->
      <el-card class="mb-4" v-if="approvalRecords.length > 0">
        <template #header>
          <span>审批历史</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="record in approvalRecords"
            :key="record.id"
            :timestamp="record.approvalTime"
            :type="getTimelineType(record.approvalStatus)"
          >
            <div>
              <div class="approval-record-header">
                <span class="approver-role">{{ getApproverTypeLabel(record.approverType) }}</span>
                <span class="approver-name">{{ record.approverName }}</span>
                <el-tag :type="getRecordStatusTagType(record.approvalStatus)" size="small">
                  {{ getRecordStatusLabel(record.approvalStatus) }}
                </el-tag>
              </div>
              <div v-if="record.approvalOpinion" class="approval-comment">
                {{ record.approvalOpinion }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 审批操作 -->
      <el-card>
        <template #header>
          <span>审批操作</span>
        </template>
        <el-form :model="approvalForm" :rules="approvalRules" ref="approvalFormRef" label-width="100px">
          <el-form-item label="审批结果" prop="approvalStatus">
            <el-radio-group v-model="approvalForm.approvalStatus">
              <el-radio label="approved">通过</el-radio>
              <el-radio label="rejected">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="approvalComment">
            <el-input
              v-model="approvalForm.approvalComment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmitApproval" :loading="submitting">确认审批</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { performApproval, getApprovalStatus } from '@/api/quotation/projectQuotationApproval'

export default {
  name: 'ApprovalDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    quotation: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'refresh'],
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      approvalRecords: [],
      currentDetailedStatus: null,
      currentDetailedStatusLabel: null,
      approvalForm: {
        approvalStatus: 'approved',
        approvalComment: ''
      },
      approvalRules: {
        approvalStatus: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        approvalComment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val && this.quotation) {
          this.loadApprovalStatus()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 加载审批状态和历史记录 */
    async loadApprovalStatus() {
      try {
        const response = await getApprovalStatus(this.quotation.id)
        if (response.code === 200) {
          this.approvalRecords = response.data.approvalRecords || []
          // 计算当前详细状态
          this.calculateCurrentStatus(response.data)
        }
      } catch (error) {
        console.error('加载审批状态失败:', error)
      }
    },
    /** 计算当前状态 */
    calculateCurrentStatus(statusData) {
      if (!statusData || !this.quotation) {
        this.currentDetailedStatus = null
        this.currentDetailedStatusLabel = null
        return
      }

      const status = this.quotation.status
      const businessType = this.quotation.businessType
      const records = statusData.approvalRecords || []

      // 根据项目状态和审批记录计算详细状态
      if (status === '0') {
        this.currentDetailedStatus = '0'
        this.currentDetailedStatusLabel = '草稿'
      } else if (status === '2') {
        this.currentDetailedStatus = '2'
        this.currentDetailedStatusLabel = '审核完成'
      } else if (status === '3') {
        this.currentDetailedStatus = '3'
        this.currentDetailedStatusLabel = '已撤回'
      } else if (status === '4') {
        this.currentDetailedStatus = '4'
        this.currentDetailedStatusLabel = '已拒绝'
      } else if (status === '1') {
        // 待审核状态，需要根据审批记录确定具体阶段
        const pendingRecords = records.filter(r => r.approvalStatus === 'pending')

        if (pendingRecords.length === 0) {
          this.currentDetailedStatus = '1'
          this.currentDetailedStatusLabel = '待审核'
        } else {
          // 找到当前待审批的类型
          const marketPending = pendingRecords.find(r => r.approverType === 'market')
          const labPending = pendingRecords.find(r => r.approverType === 'lab')
          const fieldPending = pendingRecords.find(r => r.approverType === 'field')

          if (marketPending) {
            this.currentDetailedStatus = '1-market'
            this.currentDetailedStatusLabel = '待审核（市场）'
          } else if (labPending && fieldPending) {
            this.currentDetailedStatus = '1-lab|field'
            this.currentDetailedStatusLabel = '待审核（实验室|现场）'
          } else if (labPending) {
            this.currentDetailedStatus = '1-lab'
            this.currentDetailedStatusLabel = '待审核（实验室）'
          } else if (fieldPending) {
            this.currentDetailedStatus = '1-field'
            this.currentDetailedStatusLabel = '待审核（现场）'
          } else {
            this.currentDetailedStatus = '1'
            this.currentDetailedStatusLabel = '待审核'
          }
        }
      } else {
        this.currentDetailedStatus = status
        this.currentDetailedStatusLabel = this.getStatusLabel(status)
      }
    },
    /** 提交审批 */
    handleSubmitApproval() {
      this.$refs.approvalFormRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            const response = await performApproval(this.quotation.id, this.approvalForm)
            if (response.code === 200) {
              this.$modal.msgSuccess('审批操作成功')
              this.handleClose()
              this.$emit('refresh')
            } else {
              this.$modal.msgError(response.msg || '审批操作失败')
            }
          } catch (error) {
            this.$modal.msgError('审批操作失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    /** 关闭弹框 */
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    /** 重置表单 */
    resetForm() {
      this.approvalForm = {
        approvalStatus: 'approved',
        approvalComment: ''
      }
      this.approvalRecords = []
      this.currentDetailedStatus = null
      this.currentDetailedStatusLabel = null
      if (this.$refs.approvalFormRef) {
        this.$refs.approvalFormRef.resetFields()
      }
    },
    /** 获取状态标签（与列表页一致） */
    getStatusLabel(status) {
      const statusMap = {
        '0': '草稿',
        '1': '待审核',
        '2': '审核完成',
        '3': '已撤回',
        '4': '已拒绝'
      }
      return statusMap[status] || '未知'
    },
    /** 获取状态标签类型（与列表页一致） */
    getStatusTagType(status) {
      const statusStr = String(status)
      if (statusStr === '0') return 'info'           // 草稿 - 灰色
      if (statusStr === '2') return 'success'        // 审核完成 - 绿色
      if (statusStr === '3') return 'warning'        // 已撤回 - 橙色
      if (statusStr === '4') return 'danger'         // 已拒绝 - 红色
      if (statusStr === '1-market') return 'primary' // 市场审批 - 蓝色
      if (statusStr === '1-lab') return 'primary'    // 实验室审批 - 蓝色
      if (statusStr === '1-field') return 'primary'  // 现场审批 - 蓝色
      if (statusStr === '1-lab|field') return 'primary' // 实验室|现场审批 - 蓝色
      return 'primary'                            // 默认待审核 - 蓝色
    },
    /** 获取审批记录状态标签 */
    getRecordStatusLabel(status) {
      const statusMap = {
        'pending': '待审批',
        'approved': '已通过',
        'rejected': '已拒绝'
      }
      return statusMap[status] || status
    },
    /** 获取审批记录状态标签类型 */
    getRecordStatusTagType(status) {
      const typeMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 获取审批人类型标签 */
    getApproverTypeLabel(type) {
      const typeMap = {
        'market': '市场审批',
        'lab': '实验室审批',
        'field': '现场审批'
      }
      return typeMap[type] || type
    },
    /** 获取时间线类型 */
    getTimelineType(status) {
      if (status === 'approved') return 'success'
      if (status === 'rejected') return 'danger'
      return 'primary'
    }
  }
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item .label {
  font-weight: bold;
  color: #606266;
}

.approval-record-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.approver-role {
  font-weight: bold;
  color: #409EFF;
}

.approver-name {
  color: #606266;
}

.approval-comment {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>