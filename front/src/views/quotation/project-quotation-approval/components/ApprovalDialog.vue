<template>
  <el-dialog
    title="报价审批"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    @close="handleClose"
  >
    <div v-if="quotation">
      <!-- 项目基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span>项目基本信息</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目编号：</span>
              <span>{{ quotation.projectCode }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目名称：</span>
              <span>{{ quotation.projectName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">业务类别：</span>
              <el-tag :type="quotation.businessType === 'sampling' ? 'primary' : 'success'">
                {{ quotation.businessType === 'sampling' ? '一般采样' : '送样' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">客户名称：</span>
              <span>{{ quotation.customerName }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">项目负责人：</span>
              <span>{{ quotation.projectManager }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <span class="label">当前状态：</span>
              <el-tag :type="getApprovalStatusType(quotation.approvalStatus)" size="small">
                {{ getApprovalStatusLabel(quotation.approvalStatus) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 审批历史记录 -->
      <el-card class="mb-4" v-if="approvalRecords.length > 0">
        <template #header>
          <span>审批历史</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="record in approvalRecords"
            :key="record.id"
            :timestamp="record.approvalTime"
            :type="getTimelineType(record.approvalStatus)"
          >
            <div>
              <div class="approval-record-header">
                <span class="approver-role">{{ getApproverRoleLabel(record.approverRole) }}</span>
                <span class="approver-name">{{ record.approverName }}</span>
                <el-tag :type="getApprovalStatusType(record.approvalStatus)" size="small">
                  {{ getApprovalStatusLabel(record.approvalStatus) }}
                </el-tag>
              </div>
              <div v-if="record.approvalComment" class="approval-comment">
                {{ record.approvalComment }}
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 审批操作 -->
      <el-card>
        <template #header>
          <span>审批操作</span>
        </template>
        <el-form :model="approvalForm" :rules="approvalRules" ref="approvalFormRef" label-width="100px">
          <el-form-item label="审批结果" prop="approvalStatus">
            <el-radio-group v-model="approvalForm.approvalStatus">
              <el-radio label="approved">通过</el-radio>
              <el-radio label="rejected">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审批意见" prop="approvalComment">
            <el-input
              v-model="approvalForm.approvalComment"
              type="textarea"
              :rows="4"
              placeholder="请输入审批意见"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmitApproval" :loading="submitting">确认审批</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { performApproval, getApprovalStatus } from '@/api/quotation/projectQuotationApproval'

export default {
  name: 'ApprovalDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    quotation: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'refresh'],
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      approvalRecords: [],
      approvalForm: {
        approvalStatus: 'approved',
        approvalComment: ''
      },
      approvalRules: {
        approvalStatus: [
          { required: true, message: '请选择审批结果', trigger: 'change' }
        ],
        approvalComment: [
          { required: true, message: '请输入审批意见', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val && this.quotation) {
          this.loadApprovalStatus()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    /** 加载审批状态和历史记录 */
    async loadApprovalStatus() {
      try {
        const response = await getApprovalStatus(this.quotation.id)
        if (response.code === 200) {
          this.approvalRecords = response.data.approvalRecords || []
        }
      } catch (error) {
        console.error('加载审批状态失败:', error)
      }
    },
    /** 提交审批 */
    handleSubmitApproval() {
      this.$refs.approvalFormRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true
          try {
            const response = await performApproval(this.quotation.id, this.approvalForm)
            if (response.code === 200) {
              this.$modal.msgSuccess('审批操作成功')
              this.handleClose()
              this.$emit('refresh')
            } else {
              this.$modal.msgError(response.msg || '审批操作失败')
            }
          } catch (error) {
            this.$modal.msgError('审批操作失败')
          } finally {
            this.submitting = false
          }
        }
      })
    },
    /** 关闭弹框 */
    handleClose() {
      this.dialogVisible = false
      this.resetForm()
    },
    /** 重置表单 */
    resetForm() {
      this.approvalForm = {
        approvalStatus: 'approved',
        approvalComment: ''
      }
      this.approvalRecords = []
      if (this.$refs.approvalFormRef) {
        this.$refs.approvalFormRef.resetFields()
      }
    },
    /** 获取审批状态标签 */
    getApprovalStatusLabel(status) {
      const statusMap = {
        'pending_market': '待市场审批',
        'pending_lab': '待实验室审批',
        'pending_field': '待现场审批',
        'market_approved': '市场已审批',
        'lab_approved': '实验室已审批',
        'field_approved': '现场已审批',
        'approved': '审批通过',
        'rejected': '审批拒绝'
      }
      return statusMap[status] || status
    },
    /** 获取审批状态标签类型 */
    getApprovalStatusType(status) {
      const typeMap = {
        'pending_market': 'warning',
        'pending_lab': 'warning',
        'pending_field': 'warning',
        'market_approved': 'info',
        'lab_approved': 'info',
        'field_approved': 'info',
        'approved': 'success',
        'rejected': 'danger'
      }
      return typeMap[status] || 'info'
    },
    /** 获取审批人角色标签 */
    getApproverRoleLabel(role) {
      const roleMap = {
        'market-approver': '市场审批',
        'lab-approver': '实验室审批',
        'field-approver': '现场审批'
      }
      return roleMap[role] || role
    },
    /** 获取时间线类型 */
    getTimelineType(status) {
      if (status === 'approved') return 'success'
      if (status === 'rejected') return 'danger'
      return 'primary'
    }
  }
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item .label {
  font-weight: bold;
  color: #606266;
}

.approval-record-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.approver-role {
  font-weight: bold;
  color: #409EFF;
}

.approver-name {
  color: #606266;
}

.approval-comment {
  color: #909399;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>