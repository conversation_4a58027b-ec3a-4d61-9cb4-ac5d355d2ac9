<template>
  <div class="app-container">
    <!-- 角色权限提示 -->
    <el-alert
      :title="getRolePermissionText()"
      type="info"
      :closable="false"
      class="mb-4"
    />

    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目编号" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目编号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户名称" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="业务类别" prop="businessType">
        <el-select
          v-model="queryParams.businessType"
          placeholder="请选择业务类别"
          clearable
          style="width: 200px"
        >
          <el-option label="一般采样" value="sampling" />
          <el-option label="送样" value="sample" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Refresh"
          @click="getList"
        >刷新</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pendingApprovalList">
      <el-table-column label="项目编号" align="center" prop="projectCode" width="120" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="业务类别" align="center" prop="businessType" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.businessType === 'sampling' ? 'primary' : 'success'">
            {{ scope.row.businessType === 'sampling' ? '一般采样' : '送样' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
      <el-table-column label="项目负责人" align="center" prop="projectManager" width="100" />
      <el-table-column label="当前状态" align="center" prop="approvalStatus" width="140">
        <template #default="scope">
          <el-tag
            :type="getStatusTagType(scope.row.detailedStatus || scope.row.status)"
            size="small"
          >
            {{ scope.row.detailedStatusLabel || getStatusLabel(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="submitTime" width="160" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleViewQuotation(scope.row)"
          >查看报价</el-button>
          <el-button
            type="text"
            icon="Check"
            @click="handleApproval(scope.row)"
            style="color: #67C23A"
          >审批</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看报价弹框 -->
    <view-quotation-dialog
      :visible="viewQuotationOpen"
      @update:visible="viewQuotationOpen = $event"
      :quotation-id="currentQuotationId"
    />

    <!-- 审批弹框 -->
    <approval-dialog
      :visible="approvalDialogOpen"
      @update:visible="approvalDialogOpen = $event"
      :quotation="currentQuotation"
      @refresh="getList"
    />
  </div>
</template>

<script>
import { getPendingApprovals } from '@/api/quotation/projectQuotationApproval'
import ViewQuotationDialog from '../project-quotation/components/ViewQuotationDialog'
import ApprovalDialog from './components/ApprovalDialog'
import { checkRole } from '@/utils/permission'
import useUserStore from '@/store/modules/user'

export default {
  name: 'ProjectQuotationApproval',
  components: {
    ViewQuotationDialog,
    ApprovalDialog
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 待审批项目列表
      pendingApprovalList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectName: null,
        projectCode: null,
        customerName: null,
        businessType: null
      },
      // 查看报价弹框
      viewQuotationOpen: false,
      currentQuotationId: null,
      // 审批弹框
      approvalDialogOpen: false,
      currentQuotation: null
    }
  },
  computed: {
    // 当前用户角色
    userRoles() {
      return useUserStore().roles || []
    },
    // 是否为管理员
    isAdmin() {
      return this.userRoles.includes('admin')
    },
    // 是否为市场审批人
    isMarketApprover() {
      return this.userRoles.includes('market-approver') || this.isAdmin
    },
    // 是否为实验室审批人
    isLabApprover() {
      return this.userRoles.includes('lab-approver') || this.isAdmin
    },
    // 是否为现场审批人
    isFieldApprover() {
      return this.userRoles.includes('field-approver') || this.isAdmin
    },
    // 是否有审批权限
    hasApprovalPermission() {
      return this.isMarketApprover || this.isLabApprover || this.isFieldApprover || this.isAdmin
    }
  },
  created() {
    // 检查用户是否有审批权限
    if (!this.hasApprovalPermission) {
      this.$modal.msgError('您没有审批权限，无法访问此页面')
      this.$router.push('/dashboard')
      return
    }
    this.getList()
  },
  methods: {
    /** 查询待审批项目列表 */
    getList() {
      this.loading = true

      // 构建查询参数
      const queryParams = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize
      }

      // 添加搜索条件（只有非空值才添加）
      if (this.queryParams.projectName) {
        queryParams.projectName = this.queryParams.projectName
      }
      if (this.queryParams.projectCode) {
        queryParams.projectCode = this.queryParams.projectCode
      }
      if (this.queryParams.customerName) {
        queryParams.customerName = this.queryParams.customerName
      }
      if (this.queryParams.businessType) {
        queryParams.businessType = this.queryParams.businessType
      }

      getPendingApprovals(queryParams).then(response => {
        if (response.code === 200) {
          const result = response.data || {}
          // 后端已经处理了权限过滤和分页，直接使用返回的数据
          this.pendingApprovalList = result.list || []
          this.total = result.total || 0
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryRef')
      this.handleQuery()
    },
    /** 查看报价 */
    handleViewQuotation(row) {
      this.currentQuotationId = row.id
      this.viewQuotationOpen = true
    },
    /** 审批操作 */
    handleApproval(row) {
      this.currentQuotation = row
      this.approvalDialogOpen = true
    },
    /** 获取状态标签 */
    getStatusLabel(status) {
      const statusMap = {
        '0': '草稿',
        '1': '待审核',
        '2': '审核完成',
        '3': '已撤回',
        '4': '已拒绝'
      }
      return statusMap[status] || '未知'
    },
    /** 获取状态标签类型 */
    getStatusTagType(status) {
      const statusStr = String(status)
      if (statusStr === '0') return 'info'           // 草稿 - 灰色
      if (statusStr === '2') return 'success'        // 审核完成 - 绿色
      if (statusStr === '3') return 'warning'        // 已撤回 - 橙色
      if (statusStr === '4') return 'danger'         // 已拒绝 - 红色
      if (statusStr === '1-market') return 'primary' // 市场审批 - 蓝色
      if (statusStr === '1-lab') return 'primary'    // 实验室审批 - 蓝色
      if (statusStr === '1-field') return 'primary'  // 现场审批 - 蓝色
      if (statusStr === '1-lab|field') return 'primary' // 实验室|现场审批 - 蓝色
      return 'primary'                            // 默认待审核 - 蓝色
    },
    /** 获取角色权限提示文本 */
    getRolePermissionText() {
      // admin用户显示特殊提示
      if (this.isAdmin) {
        return '当前角色：系统管理员 - 您拥有所有报价审批权限，可以查看和审批所有类型的报价单'
      }

      const roleTexts = []
      if (this.userRoles.includes('market-approver')) {
        roleTexts.push('市场审批人')
      }
      if (this.userRoles.includes('lab-approver')) {
        roleTexts.push('实验室审批人')
      }
      if (this.userRoles.includes('field-approver')) {
        roleTexts.push('现场审批人')
      }
      return `当前角色：${roleTexts.join('、')} - 您可以审批对应类型的报价单`
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>