<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item label="部门名称" prop="deptName">
            <el-input
               v-model="queryParams.deptName"
               placeholder="请输入部门名称"
               clearable
               style="width: 200px"
               @keyup.enter="handleQuery"
            />
         </el-form-item>
         <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="部门状态" clearable style="width: 200px">
               <el-option
                  v-for="dict in sys_normal_disable"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
               />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button
               type="primary"
               plain
               icon="Plus"
               @click="handleAdd"
               v-hasPermi="['system:dept:add']"
            >新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button
               type="info"
               plain
               icon="Sort"
               @click="toggleExpandAll"
            >展开/折叠</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table
         v-if="refreshTable"
         v-loading="loading"
         :data="deptList"
         row-key="deptId"
         :default-expand-all="isExpandAll"
         :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
         <el-table-column prop="deptName" label="部门名称" width="260"></el-table-column>
         <el-table-column prop="orderNum" label="排序" width="200"></el-table-column>
         <el-table-column label="负责人" width="180">
            <template #default="scope">
               <span v-if="scope.row.leaders && scope.row.leaders.length > 0">
                  {{ getLeaderNames(scope.row.leaders).join(', ') }}
               </span>
               <span v-else-if="scope.row.leader">
                  {{ getUserNameById(scope.row.leader) }}
               </span>
               <span v-else>-</span>
            </template>
         </el-table-column>
         <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="创建时间" align="center" prop="createTime" width="200">
            <template #default="scope">
               <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:dept:edit']">修改</el-button>
               <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:dept:add']">新增</el-button>
               <el-button v-if="scope.row.parentId != 0" link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:dept:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或修改部门对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="deptRef" :model="form" :rules="rules" label-width="80px">
            <el-row>
               <el-col :span="24" v-if="form.parentId !== 0">
                  <el-form-item label="上级部门" prop="parentId">
                     <el-tree-select
                        v-model="form.parentId"
                        :data="deptOptions"
                        :props="{ value: 'deptId', label: 'deptName', children: 'children' }"
                        value-key="deptId"
                        placeholder="选择上级部门"
                        check-strictly
                     />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="部门名称" prop="deptName">
                     <el-input v-model="form.deptName" placeholder="请输入部门名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="显示排序" prop="orderNum">
                     <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="负责人" prop="leaders">
                     <el-select
                        v-model="form.leaders"
                        placeholder="请选择负责人"
                        filterable
                        clearable
                        multiple
                        collapse-tags
                        collapse-tags-tooltip
                        style="width: 100%"
                        @visible-change="getUserList"
                     >
                        <el-option
                           v-for="item in userOptions"
                           :key="item.userId"
                           :label="item.nickName"
                           :value="item.userId"
                        >
                           <span>{{ item.nickName }}</span>
                           <span style="float: right; color: #8492a6; font-size: 13px">{{ item.userName }}</span>
                        </el-option>
                     </el-select>
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="联系电话" prop="phone">
                     <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                     <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="部门状态">
                     <el-radio-group v-model="form.status">
                        <el-radio
                           v-for="dict in sys_normal_disable"
                           :key="dict.value"
                           :value="dict.value"
                        >{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Dept">
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild } from "@/api/system/dept";
import { listUser, getUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);
const userOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    deptName: undefined,
    status: undefined
  },
  rules: {
    parentId: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    orderNum: [{ required: true, message: "显示排序不能为空", trigger: "blur" }],
    email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
    phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
  },
});

const { queryParams, form, rules } = toRefs(data);

// 用户ID到用户名称的映射
const userIdToName = ref({});

/** 根据用户ID获取用户名称 */
function getUserNameById(userId) {
  if (!userId) return "";
  return userIdToName.value[userId] || userId;
}

/** 根据用户ID列表获取用户名称列表 */
function getLeaderNames(userIds) {
  if (!userIds || !Array.isArray(userIds)) return [];
  return userIds.map(userId => getUserNameById(userId)).filter(name => name);
}

/** 查询部门列表 */
function getList() {
  loading.value = true;
  listDept(queryParams.value).then(response => {
    deptList.value = proxy.handleTree(response.data, "deptId");
    loading.value = false;
    // 获取所有负责人的用户信息
    loadUserNames();
  });
}

/** 加载用户名称 */
function loadUserNames() {
  // 收集所有用户ID
  const userIds = new Set();
  const collectUserIds = (depts) => {
    depts.forEach(dept => {
      // 支持多个负责人
      if (dept.leaders && Array.isArray(dept.leaders)) {
        dept.leaders.forEach(leaderId => userIds.add(leaderId));
      }
      // 兼容单个负责人
      if (dept.leader) userIds.add(dept.leader);
      if (dept.children && dept.children.length) collectUserIds(dept.children);
    });
  };
  collectUserIds(deptList.value);
  
  // 获取用户信息
  userIds.forEach(userId => {
    if (!userIdToName.value[userId]) {
      listUser({ userId: userId }).then(response => {
        if (response.rows && response.rows.length > 0) {
          userIdToName.value[userId] = response.rows[0].nickName;
        }
      });
    }
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 获取用户列表 */
function getUserList(open) {
  if (open) {
    userOptions.value = [];
    listUser({}).then(response => {
      userOptions.value = response.rows;
      // 更新用户ID到名称的映射
      response.rows.forEach(user => {
        userIdToName.value[user.userId] = user.nickName;
      });
      
      // 处理多个负责人的情况
      if (form.value.leaders && Array.isArray(form.value.leaders)) {
        // 获取所有不在下拉框中的负责人信息
        const missingLeaderIds = form.value.leaders.filter(leaderId => !userIdToName.value[leaderId]);
        
        // 逐个获取缺失的负责人信息
        missingLeaderIds.forEach(leaderId => {
          getUser(leaderId).then(res => {
            if (res.data) {
              userIdToName.value[leaderId] = res.data.nickName;
              // 如果用户不在当前列表中，添加到选项中
              if (!userOptions.value.find(u => u.userId === leaderId)) {
                userOptions.value.push(res.data);
              }
            }
          });
        });
      }
      // 兼容单个负责人的情况
      else if (form.value.leader && !userIdToName.value[form.value.leader]) {
        getUser(form.value.leader).then(res => {
          if (res.data) {
            userIdToName.value[form.value.leader] = res.data.nickName;
            // 如果用户不在当前列表中，添加到选项中
            if (!userOptions.value.find(u => u.userId === form.value.leader)) {
              userOptions.value.push(res.data);
            }
          }
        });
      }
    });
  }
}
/** 表单重置 */
function reset() {
  form.value = {
    deptId: undefined,
    parentId: undefined,
    deptName: undefined,
    orderNum: 0,
    leaders: [],
    phone: undefined,
    email: undefined,
    status: "0"
  };
  proxy.resetForm("deptRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  listDept().then(response => {
    deptOptions.value = proxy.handleTree(response.data, "deptId");
  });
  if (row != undefined) {
    form.value.parentId = row.deptId;
  }
  open.value = true;
  title.value = "添加部门";
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  listDeptExcludeChild(row.deptId).then(response => {
    deptOptions.value = proxy.handleTree(response.data, "deptId");
  });
  getDept(row.deptId).then(response => {
    form.value = response.data;
    
    // 处理负责人数据兼容性
    if (response.data.leaders && Array.isArray(response.data.leaders)) {
      form.value.leaders = response.data.leaders;
    } else if (response.data.leader) {
      // 兼容旧版本单个负责人
      form.value.leaders = [response.data.leader];
    } else {
      form.value.leaders = [];
    }
    
    // 确保在打开编辑表单时加载用户列表，以便正确显示负责人名称
    getUserList(true);
    open.value = true;
    title.value = "修改部门";
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["deptRef"].validate(valid => {
    if (valid) {
      if (form.value.deptId != undefined) {
        updateDept(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addDept(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.deptName + '"的数据项?').then(function() {
    return delDept(row.deptId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

getList();
</script>
