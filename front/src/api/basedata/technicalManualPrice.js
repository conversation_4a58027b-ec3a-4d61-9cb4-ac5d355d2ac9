import request from '@/utils/request'

// 查询技术手册价格列表
export function listTechnicalManualPrice(query) {
  return request({
    url: '/basedata/technical-manual-price/list',
    method: 'get',
    params: query
  })
}

// 查询技术手册价格分页列表
export function pageTechnicalManualPrice(query) {
  return request({
    url: '/basedata/technical-manual-price/page',
    method: 'get',
    params: query
  })
}

// 查询技术手册价格详细
export function getTechnicalManualPrice(id) {
  return request({
    url: '/basedata/technical-manual-price/' + id,
    method: 'get'
  })
}

// 新增技术手册价格
export function addTechnicalManualPrice(data) {
  return request({
    url: '/basedata/technical-manual-price',
    method: 'post',
    data: data
  })
}

// 修改技术手册价格
export function updateTechnicalManualPrice(data) {
  return request({
    url: '/basedata/technical-manual-price',
    method: 'put',
    data: data
  })
}

// 删除技术手册价格
export function delTechnicalManualPrice(id) {
  return request({
    url: '/basedata/technical-manual-price/' + id,
    method: 'delete'
  })
}

// 获取检测方法和类别选项
export function getMethodCategoryOptions() {
  return request({
    url: '/basedata/technical-manual-price/options/method-categories',
    method: 'get'
  })
}

// 导出技术手册价格
export function exportTechnicalManualPrice(query) {
  return request({
    url: '/basedata/technical-manual-price/export',
    method: 'get',
    params: query
  })
}
